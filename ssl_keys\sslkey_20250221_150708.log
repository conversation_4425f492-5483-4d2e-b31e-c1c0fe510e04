# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET 28e796b44e955d9b90f0ebb18a65275d509c15d044a5f5bf044a19b38ef068d1 8fb20a14c743ac4bdb6ca6142410189455a445c8ab47d07edf3a5a09073b2f3e48724ef666234c1a2982bbef2f92e3ea
EXPORTER_SECRET 28e796b44e955d9b90f0ebb18a65275d509c15d044a5f5bf044a19b38ef068d1 5594cb10f5138da60eb31cb38b62cfe5a4417744bd5f470f3831fcc06289ca51f0fc3c13306d708c24fafcc63755e389
SERVER_TRAFFIC_SECRET_0 28e796b44e955d9b90f0ebb18a65275d509c15d044a5f5bf044a19b38ef068d1 c7ffcfe109dbea6a9dda05bb401c9dfe230639fa2f08e80baa8cd8246c7fe7e35b6cb37b07a1568d271d71e68105ae84
CLIENT_HANDSHAKE_TRAFFIC_SECRET 28e796b44e955d9b90f0ebb18a65275d509c15d044a5f5bf044a19b38ef068d1 35d3a0bc6868d0162375a61a944eada258f5408edf8c48f689f895670313360567c91a30978410675e3a3b024a05d71a
CLIENT_TRAFFIC_SECRET_0 28e796b44e955d9b90f0ebb18a65275d509c15d044a5f5bf044a19b38ef068d1 b8909ecfa23a20872c3302cb35374679b334a81823e76cb3a28811bcca414c4cc8256ee247afc6763e46ef8be115b607
