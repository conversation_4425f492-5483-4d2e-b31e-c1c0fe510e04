# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 8d832bef17483f9ba0699a7abc3b8b18126d6c1e7e7d6c614ae69293e9cb7cb9 a3fbd3122335450a8c2c8890babb02ee9c9a693f5249007ef4803a34b1cebd5edeecf4f464e893c4e904297643000d1c
EXPORTER_SECRET 8d832bef17483f9ba0699a7abc3b8b18126d6c1e7e7d6c614ae69293e9cb7cb9 5cdaf29636532a6c44143c63447e4ad181b4e5feb0c328e9c921bbfdee8c03df664f7837aee85aab998e9d784f22a6e8
SERVER_TRAFFIC_SECRET_0 8d832bef17483f9ba0699a7abc3b8b18126d6c1e7e7d6c614ae69293e9cb7cb9 c5335e6f0dc5c46bde0d9a01d323a95b7c5689f552521c311ac718fee960ea34da39a105d4bc313203f9871bfc6cd3d6
CLIENT_HANDSHAKE_TRAFFIC_SECRET 8d832bef17483f9ba0699a7abc3b8b18126d6c1e7e7d6c614ae69293e9cb7cb9 07a8a6fc1776c06dc7ccc220c8da6e971a77a7bfb742dca25d95dada0cd56a93217741e39fdb89c228bafb0acc38cdf0
CLIENT_TRAFFIC_SECRET_0 8d832bef17483f9ba0699a7abc3b8b18126d6c1e7e7d6c614ae69293e9cb7cb9 8d14192e0906f609f93ad3caee0a41e714724c5ab3a58175a2c95c83748648cc916b38f45c250cdb491cb50c540df98e
