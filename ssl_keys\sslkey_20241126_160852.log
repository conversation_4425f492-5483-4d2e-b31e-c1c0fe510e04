# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON><PERSON><PERSON>_TRAFFIC_SECRET 5332623a04d64fdd5ff4d36915311ca58371ecf294daac4015292477cf9b2ffc 2ac9a1909e650039d9be091041539d6bea06b95e8b2e13b6c96c0d5b1b89142228bf6a3792cac2f699672e6915f8df76
EXPORTER_SECRET 5332623a04d64fdd5ff4d36915311ca58371ecf294daac4015292477cf9b2ffc a3a8b7d43a2b8cbce1d005ad8db001439890134027eee09965833a044532bedfeb4a7d27d3534d7929806d5394c13339
SERVER_TRAFFIC_SECRET_0 5332623a04d64fdd5ff4d36915311ca58371ecf294daac4015292477cf9b2ffc 5479c59fd24fb8d092c864746400bb628c40d6ffcd394c9b461c28ba192519ba9b8d36ee2ab5922c894a2e6379a9fe68
CLIENT_HANDSHAKE_TRAFFIC_SECRET 5332623a04d64fdd5ff4d36915311ca58371ecf294daac4015292477cf9b2ffc b56964faa109d3e1b861cce75c560c9af93eaf5e35ce2f26fef73e820d28c9ae68295e9d437424ff3149d2b2d40d3f0b
CLIENT_TRAFFIC_SECRET_0 5332623a04d64fdd5ff4d36915311ca58371ecf294daac4015292477cf9b2ffc db5561275a9432d3411ddbb4e746deb12d8664445de51414d2d42b970ee3eb6c4cfb7c18c9d4d44298459c9e88fde877
