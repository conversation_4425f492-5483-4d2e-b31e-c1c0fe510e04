# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET 4c99397803ab1533fe7f6961d4b3905ada5cfb452119370e45810c07fc1787af cbb7d1aa242599eb5545fff54de6762cd4b44d780a216fb6dd86ece772356d3019708f183d8c5b290efd1ef3d299d3d5
EXPORTER_SECRET 4c99397803ab1533fe7f6961d4b3905ada5cfb452119370e45810c07fc1787af a0877c06d3012a840e66f37e0b3dd21215dd1f80924d49dfe8b08172fc73b2f9f109b4a2708df2b1fad089921a95b951
SERVER_TRAFFIC_SECRET_0 4c99397803ab1533fe7f6961d4b3905ada5cfb452119370e45810c07fc1787af 4b93f428edc5f439ed563daab74054f050f30b132ef67a5114a4dcb3964ee7dd0728398ac84f414fd93b5bdecd5b3cd7
CLIENT_HANDSHAKE_TRAFFIC_SECRET 4c99397803ab1533fe7f6961d4b3905ada5cfb452119370e45810c07fc1787af 2070b38a7a823a961a6688bd3bea1c98d182001af4505126882f7f4a7d8a0eecaeebe12decff201bc9d2b660fc15ce8d
CLIENT_TRAFFIC_SECRET_0 4c99397803ab1533fe7f6961d4b3905ada5cfb452119370e45810c07fc1787af ca0f2cc6dc04881a6edd8001f179634e5da770306013b457b10c23ca6b770c8dbded9ac34d2def3df4069cdfbd5f09a5
