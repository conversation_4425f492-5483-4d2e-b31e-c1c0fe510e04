# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET c34330db07a62d9a1e81c14c7719685ab9f014d1ef797a7fb92f3e1edb0739ab eaa99e1ce86fc26767d69cd142f144e4a76a3166b4ed72f325d37f02c3e0238878975e426d4fae6b0a79dc01ba8fffe4
EXPORTER_SECRET c34330db07a62d9a1e81c14c7719685ab9f014d1ef797a7fb92f3e1edb0739ab 3ad4550989c0eae0881eb6dc538963127c0c3bc127c0c823782536b6ec1beccd3b6464c7047ba617de5d5b1b66a3abbc
SERVER_TRAFFIC_SECRET_0 c34330db07a62d9a1e81c14c7719685ab9f014d1ef797a7fb92f3e1edb0739ab 8a9cc2ace31757944838102819866d56f49272e559f1b65f9145892e895547e7d838e608616bb63ddf877e889942b9c6
CLIENT_HANDSHAKE_TRAFFIC_SECRET c34330db07a62d9a1e81c14c7719685ab9f014d1ef797a7fb92f3e1edb0739ab c9e463ad896aa3eff001abaa88bd1e643c0534bed35f2534b00ee0ad446379e77faa995d5b13a7b6f71fe7a1a4c4e7c5
CLIENT_TRAFFIC_SECRET_0 c34330db07a62d9a1e81c14c7719685ab9f014d1ef797a7fb92f3e1edb0739ab 0e2b3b75a128d2ddb942baf2abdbba022931c57707e738307d5f1f851e94d8c858cfb5066f415cb6c6a619bb1a1bf9c0
