# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET f5d303c9c023172b36b3081aaf89347a8184b8056518a6a7fd3838aa7c3bf4c0 25ed795f142bea91a6de9e59b0d2ea30c5cc39092409ff9aa116f0037ca6d98c825a00766bd6a23cbbffa572bdb0ab8a
EXPORTER_SECRET f5d303c9c023172b36b3081aaf89347a8184b8056518a6a7fd3838aa7c3bf4c0 c1bebee3cc0384cdcdf9316fe95fc12ff3f1fbd50905ba83a9b06aa4dcbfb45b2a4401fcb1c26d67b61e26b29f226a9f
SERVER_TRAFFIC_SECRET_0 f5d303c9c023172b36b3081aaf89347a8184b8056518a6a7fd3838aa7c3bf4c0 4e9a1ab91f4372a432316beb1a1e97a84ba602ba4399f91368a511ece421ae03347972a788808100f41756201b6ac1b1
CLIENT_HANDSHAKE_TRAFFIC_SECRET f5d303c9c023172b36b3081aaf89347a8184b8056518a6a7fd3838aa7c3bf4c0 eafd35ceec80fb5966ac3f4ca4371c50eb34edbcda6d71221a4b064693c85b0a40dd1ef5141e203776c5b363c281309f
CLIENT_TRAFFIC_SECRET_0 f5d303c9c023172b36b3081aaf89347a8184b8056518a6a7fd3838aa7c3bf4c0 c3b5b618ced94b3ebea2c514fc219b9b151bd5ccc4e75dfba83f1ef2dc34fde9f96e6ba1abdd96fa449b046258bd011b
