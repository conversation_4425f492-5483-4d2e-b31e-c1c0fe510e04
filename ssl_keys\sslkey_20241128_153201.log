# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET fb821a9f5b3eb8e714109df5bef509fed1b5bf266396c4ea3e21cadec8577154 6b19344184cc1c31b4c19b94a672ffa0aace0e29aae5b949e3f5cafdda16c6eb0e823008eec2ade5ec4fc443bcab760a
EXPORTER_SECRET fb821a9f5b3eb8e714109df5bef509fed1b5bf266396c4ea3e21cadec8577154 95ef4342075088d4e5591c33408784ced7ae70b552e26cc5fff1514344e296277789e91fd86ab7a7d4e9592765ef578a
SERVER_TRAFFIC_SECRET_0 fb821a9f5b3eb8e714109df5bef509fed1b5bf266396c4ea3e21cadec8577154 21e32cf38b66c877016aa39db3f492256bb9e04b6262468217000a5e735ea7f26faa168cfd59adf05c832d4947f86d59
CLIENT_HANDSHAKE_TRAFFIC_SECRET fb821a9f5b3eb8e714109df5bef509fed1b5bf266396c4ea3e21cadec8577154 b466b166c3381fb92c6380a694e061b7c4ba040f47ad0128c97bdd7641677c0ee8b7ba7bb0f18a4bddbb7103fca35846
CLIENT_TRAFFIC_SECRET_0 fb821a9f5b3eb8e714109df5bef509fed1b5bf266396c4ea3e21cadec8577154 e51abb045ac5ca4e48dde4943eb7ec118ec986fa19b3905fd768c9b70c5e2cf8ddfbb59d26ec90601ee90145d1f06526
