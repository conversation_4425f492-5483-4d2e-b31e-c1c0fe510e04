# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON><PERSON><PERSON>_TRAFFIC_SECRET 1d46dfcd4631eed4bdd147761d48dd8865f4316a02419d7c9c0ce9cf4371212b 4d58112ec16c8272cebad63301bbf911780e90045bb3ebc562dac405df42f06c80977e713c7781d58151b3f18730be6a
EXPORTER_SECRET 1d46dfcd4631eed4bdd147761d48dd8865f4316a02419d7c9c0ce9cf4371212b 15c5dc45e8c8cda336f3255c9aeba05e2211141abf2e6443373e00ef18fd97200caad0c0cf589ae67881f54deee53dd1
SERVER_TRAFFIC_SECRET_0 1d46dfcd4631eed4bdd147761d48dd8865f4316a02419d7c9c0ce9cf4371212b 2379a8c5af2a453ee27dcd832e418be7d0f03f12cef63697baeeb51a10d45d06fa9118bb1ee895f8c27a53f2ea5d81ba
CLIENT_HANDSHAKE_TRAFFIC_SECRET 1d46dfcd4631eed4bdd147761d48dd8865f4316a02419d7c9c0ce9cf4371212b 0845b761645ee6c02c14948c345eea6211e22699749438355489dd575d0e646c59b976ccdc80da178c57be050b60da57
CLIENT_TRAFFIC_SECRET_0 1d46dfcd4631eed4bdd147761d48dd8865f4316a02419d7c9c0ce9cf4371212b 738c26c17a84db5aac9a444750a01a92e7c437808eafaf3ee8f88c6c85cf164c5082685d6162be3fcb0293cdf1e5cdb0
