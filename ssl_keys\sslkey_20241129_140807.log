# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 470f1b526c1d9ee1e88bdb149ed4f08f8e59a5861891b53d9d22c732fc60ab03 1442a09d0aea77012c86409d81afe83e42669a8ecaa34fb3b689a8f4eb2cbf16e98d21753c56b140e387e94ef4955a41
EXPORTER_SECRET 470f1b526c1d9ee1e88bdb149ed4f08f8e59a5861891b53d9d22c732fc60ab03 6de6961331fd84e8d2c9b376af494e83a65cfe4801bb0583b3831dbd5c163151f080edbb5c3dde07a796744cb4d322cf
SERVER_TRAFFIC_SECRET_0 470f1b526c1d9ee1e88bdb149ed4f08f8e59a5861891b53d9d22c732fc60ab03 98bf535060d053aff9465f63bdb3bc220b11a2ccdc5f2b1fa8f64fd8a389ce3dc686bfcddc51fcab69ff625e3aab5c1d
CLIENT_HANDSHAKE_TRAFFIC_SECRET 470f1b526c1d9ee1e88bdb149ed4f08f8e59a5861891b53d9d22c732fc60ab03 ac51dca3eb9821a091f899fc6036ffc667ef3f63fe6990d2077a893c35243e3ac6bde0ab51bfd676fdd0e9f76b12d25d
CLIENT_TRAFFIC_SECRET_0 470f1b526c1d9ee1e88bdb149ed4f08f8e59a5861891b53d9d22c732fc60ab03 34ee0df9969a7ceb0a3659d279d457c1fab71f57138f2a54dc85896cba4c67297bbc960dc92f326eb1d9d74d11eaf2db
