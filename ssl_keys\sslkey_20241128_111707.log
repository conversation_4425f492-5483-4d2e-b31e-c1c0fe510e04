# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 30a41f32d7bbf3fa783fdcbdb1c71cbeba5d3b4de1e0d7ae7eb67e3b021f7489 bfa11877e96c00438c5f1938290894cb22bdd43adc17b40a08ef0b68a5a5775456601c007bec578ae16f1d4352552128
EXPORTER_SECRET 30a41f32d7bbf3fa783fdcbdb1c71cbeba5d3b4de1e0d7ae7eb67e3b021f7489 f862e0df86d55cde7cefc7aeb8f9574e8990fbd7ce565967f96ab9898e7ae6b2a895b0f5eac9e657b10a4bdf63096b4d
SERVER_TRAFFIC_SECRET_0 30a41f32d7bbf3fa783fdcbdb1c71cbeba5d3b4de1e0d7ae7eb67e3b021f7489 c1dde60474b2868e79ba42e08d6714749fca208ff07e2ff629c2272fc6b003248d1525e34161671da0ddfe0dc55776a7
CLIENT_HANDSHAKE_TRAFFIC_SECRET 30a41f32d7bbf3fa783fdcbdb1c71cbeba5d3b4de1e0d7ae7eb67e3b021f7489 f1e1a308f3a388de7cb628e0ff81bd6a9701eb086d92aaf89465294927f985a7641a31c7602876120e9dc4011ab63a4b
CLIENT_TRAFFIC_SECRET_0 30a41f32d7bbf3fa783fdcbdb1c71cbeba5d3b4de1e0d7ae7eb67e3b021f7489 f43f33c24e205966e236dc1812db9912763124b183eb32dad15ff2bffb69f46b8715cb64382cf3a7e60badf026b5af58
