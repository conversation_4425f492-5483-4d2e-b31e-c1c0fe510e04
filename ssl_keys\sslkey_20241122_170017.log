# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET c10f6b2b617774dad539c91b2e00e80c3c5735c2d98eabd922a89f2df46b2765 76c9bf394557a829973d86eeb6aebb08878992e5b5a8e54c61b808a6073a578f20e1343c33f72f84aba16d77bebc4f18
EXPORTER_SECRET c10f6b2b617774dad539c91b2e00e80c3c5735c2d98eabd922a89f2df46b2765 1b7d5c804591fc48088076adfd4e34fc941d493798185f56aa7d009643f274dac54b60386a03f5b9df0ed4bf11b86be4
SERVER_TRAFFIC_SECRET_0 c10f6b2b617774dad539c91b2e00e80c3c5735c2d98eabd922a89f2df46b2765 4b953fda96a83e5b477738220c4640f14a3d97fcba0269a454a0926b533d41d3756ad57dd3c59c12d13c8289b2e34c6e
CLIENT_HANDSHAKE_TRAFFIC_SECRET c10f6b2b617774dad539c91b2e00e80c3c5735c2d98eabd922a89f2df46b2765 f4b11bcb385c6cee26c1d66e63ff4acb841c42776abd748edb0cc6eb22d47699e23623f2025c116ab8ac61b820ed0fdd
CLIENT_TRAFFIC_SECRET_0 c10f6b2b617774dad539c91b2e00e80c3c5735c2d98eabd922a89f2df46b2765 592181d6099b392012974f5d696a70a4363c9018af1dfd935ecc13f073f37fdc605efbc81cd033b22cdee636ad9cae99
