# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 3232bb95883ca27a82eea41d1cc60d4c2f11cb967033bfe82ffd3468e3e89378 69d069a394df321f28679a922f6ac3f74a9981badae05f29fddbfdaa02cdbf96388a27ac64431d98fc437bbb2b3d564b
EXPORTER_SECRET 3232bb95883ca27a82eea41d1cc60d4c2f11cb967033bfe82ffd3468e3e89378 fb1d1ca091ccad31bd90b114755948adac4aad2c9612e8a963dbadbf73f30e3a396832fee30df8831f3a7f0a90ff89cc
SERVER_TRAFFIC_SECRET_0 3232bb95883ca27a82eea41d1cc60d4c2f11cb967033bfe82ffd3468e3e89378 dcb3dc22c321a56a37a33f43d382ab3d22ec424f00315eb5a2797c37fe3a41a185cb1aedfa0cbf6f282d2ce09a4e298b
CLIENT_HANDSHAKE_TRAFFIC_SECRET 3232bb95883ca27a82eea41d1cc60d4c2f11cb967033bfe82ffd3468e3e89378 1621fe79d5daccad7770eb8e46ccfcfdb18c3735a963fc705e90207a75532ff84c3da9fabca96f9294ab5da341031353
CLIENT_TRAFFIC_SECRET_0 3232bb95883ca27a82eea41d1cc60d4c2f11cb967033bfe82ffd3468e3e89378 07e03ee008a663e4f0ab4e45a74be422884c4c7a68bed5b3e8f70618009cc862f3b6f7dee8975d070d9606c193a7721c
