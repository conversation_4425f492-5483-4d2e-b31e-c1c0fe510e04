connect ********** 3496 Tls_DoipOver_test.crt Tls_DoipOver_test.key

vin:HJNFBABN3RP000420

VDF/SAF: root h2nlDAML.CEs

车主账号：<EMAIL>
密码：pan_gu@123456
安全密码：112233

vid：62b79c66264949e11452975030303010

默认key
c54d4577632faa6a2747d131b9210666
set -t 0005 -k 4177d2b88694a5351f6e982acac1c61b --mask 7fd2  VDF
set -t 005f -k d518227ebbcc478110a26580ed2aee97 --mask 6bca  SAF 
set -t 0023 -k  --mask 3689  ADC
set -t 0032 -k 63666bd10603af4148182809a1b293c4 --mask 19db  CDC

请求OBD防火墙状态：
send 1003 2709 270a --s2k 31033386 223375

SSH disable:
send 1003 2709 270a --s2k 310133870000

SSH enable:
send 1003 2709 270a --s2k 310133870001

OBD防火墙disable：
send 1003 2709 270a --s2k 3101338601

OBD防火墙enable：
send 1003 2709 270a --s2k 31023386


VDF吊销传入错误路径：
send 1003 2709 270a --s2k 310133bc00075644462f746c73 310133bc00075644462f647663 (查询证书状态)
send 1003 2709 270a --s2k 3101430400074344432f746c73 3101430400074344432f647663 (吊销VDF传入错误（cdc）路径)


openssl genpkey -algorithm RSA -out cert.key -pkeyopt rsa_keygen_bits:2048
openssl req -new -key cert.key -out cert.csr
openssl x509 -req -days 365 -in cert.csr -signkey cert.key -out cert.crt
