# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET 5991063fab7b08de04b27ae5f50ccd2cacf299c7c74acee54574e06b6ffa5e5a 9014467a015edbf82235b0bb4706f42112d5f37565c9dfc2b393cebcf324e0a3cd066e7826d4a9397d4993e9793f9ddd
EXPORTER_SECRET 5991063fab7b08de04b27ae5f50ccd2cacf299c7c74acee54574e06b6ffa5e5a 6940908d34e2525585e9dbdb93a3e88d78c07ecb355d9af0c12025db39d67680e54fc9ce37b79ef26a3e5f439a4114f4
SERVER_TRAFFIC_SECRET_0 5991063fab7b08de04b27ae5f50ccd2cacf299c7c74acee54574e06b6ffa5e5a d36b7ae3c848362a302c365204ae1561ad558c81401d6053ba09387acf336adbff429defcd8cc50878e0508155cc8ed1
CLIENT_HANDSHAKE_TRAFFIC_SECRET 5991063fab7b08de04b27ae5f50ccd2cacf299c7c74acee54574e06b6ffa5e5a 6ec6671661ffb36b3b752c4917e147a60a0c14f20d26f5d88a199d8e9bbf4ca2def43b9c9cad172e6bd607149689268e
CLIENT_TRAFFIC_SECRET_0 5991063fab7b08de04b27ae5f50ccd2cacf299c7c74acee54574e06b6ffa5e5a 9bf7023358260bfa490dc414a6a1cdeffe55fcfc97d732973b1a8c0ba7bca83630aae0d95b88d1c323cdd1bfaffda55b
