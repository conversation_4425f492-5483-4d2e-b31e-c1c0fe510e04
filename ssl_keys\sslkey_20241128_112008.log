# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET 4b00ee4613786e2997189f44132d1969f4c5b97d7489854042b1a53dffeb260b afe8852d2d8937c7ac47f143f45b617bc0f0e40cf48d64a27042784f259b0cb9df529eb3f9408c21b2670270931e8f2d
EXPORTER_SECRET 4b00ee4613786e2997189f44132d1969f4c5b97d7489854042b1a53dffeb260b 43f0182ecbf603435b38116846c087cf46ac90c7b8332ef442dc12ce14a58c5013f65c3276e8d7cdbc7ff7f11d278e91
SERVER_TRAFFIC_SECRET_0 4b00ee4613786e2997189f44132d1969f4c5b97d7489854042b1a53dffeb260b 757a123e53532722bf0c7d79fed0f3c305797ec664c0b9b6a72830fd1fd64c8bb6a04f92c1908f5cccd6db246fec0c76
CLIENT_HANDSHAKE_TRAFFIC_SECRET 4b00ee4613786e2997189f44132d1969f4c5b97d7489854042b1a53dffeb260b 1b963d002c8a639a5f4e5e835a13cf45b57c5de998be327319ef86ca0c40564e6f095065026e8b1f339bee4db0cd0bd2
CLIENT_TRAFFIC_SECRET_0 4b00ee4613786e2997189f44132d1969f4c5b97d7489854042b1a53dffeb260b d838dead327b72599068e366bb8329bfed05154932868585f37baae9dfe0d613e0514bc8250484cb6e4b49f3137d90c7
