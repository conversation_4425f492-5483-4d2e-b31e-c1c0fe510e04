# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 8a2bf71e683d8bf7309d6686f3211ca488beec4ae40102d832139def854d69c0 408b88021d8abcaffe817c661ae835f12c575a7a8eb029172702dbb102a1483e144ef5e09f3536d046bd561f5dc726d3
EXPORTER_SECRET 8a2bf71e683d8bf7309d6686f3211ca488beec4ae40102d832139def854d69c0 f32bcf2fbb00e3bccb82a1002d7d60408aa1087042b9b4848b224e1e691ef32b6e98ccc911bb4c7c8eaf9d7729dee72e
SERVER_TRAFFIC_SECRET_0 8a2bf71e683d8bf7309d6686f3211ca488beec4ae40102d832139def854d69c0 ba36983be6f0758e35bf75a3c49a38f7f7d272ee5d7b0de6e2f86150c2aee47720c38deb36f634d31dd9c79a0c09d96f
CLIENT_HANDSHAKE_TRAFFIC_SECRET 8a2bf71e683d8bf7309d6686f3211ca488beec4ae40102d832139def854d69c0 bec02d34e921b51603a88cbfe591ba724f1ee524a6848004ac9cef92214ff1ab486c8155de75f4252a9c8ef72a22b4a8
CLIENT_TRAFFIC_SECRET_0 8a2bf71e683d8bf7309d6686f3211ca488beec4ae40102d832139def854d69c0 b4d014faa333efbce6623904ff8e1b2a1255abd178dc0cd4956013fca0f8053620274765870e89967b8eade1017841d4
