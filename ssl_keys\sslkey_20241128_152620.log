# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET e0bbe861149db221b01057021e28306bd85a08b39106331808ffd7f16ff3d21a 95e6bc7c54bf7d484100782a70eb74704fd25ce2d4d46bef8a38d368d7e4ba6b6a60d60dd98ad6dfc4a37d8cc3273fd4
EXPORTER_SECRET e0bbe861149db221b01057021e28306bd85a08b39106331808ffd7f16ff3d21a d9f94981a6ce3078952eec198f23b49ad87267448627cde507da48e9e4fedf32a917d3190b2fa4f90ace0570c14e033c
SERVER_TRAFFIC_SECRET_0 e0bbe861149db221b01057021e28306bd85a08b39106331808ffd7f16ff3d21a 38b2cf160c4149a95c8fbb487d94f0b9125715e6ca3eeb7caae3c0a79be4d525ba445da74879efa665a13d3377eb25da
CLIENT_HANDSHAKE_TRAFFIC_SECRET e0bbe861149db221b01057021e28306bd85a08b39106331808ffd7f16ff3d21a 29eb4b514b477635684efa7e3dcde590a63a103754913744180784508fdd10005247d26ef1e29b94dce264a89243aa5c
CLIENT_TRAFFIC_SECRET_0 e0bbe861149db221b01057021e28306bd85a08b39106331808ffd7f16ff3d21a faeb3ce7c7c5054dbd4bcf53e151a80b5e448d58ee9334e2d812653b8405c23bd49c9a2aaaa5ed07378dd59d04947d9e
