import requests
import urllib3
from datetime import datetime
import os
import json
import time
import random

# 移除所有日志配置，使验证过程对用户无感知

# 禁用不安全请求警告
os.environ["NO_PROXY"] = "api.m.taobao.com, quan.suning.com, worldtimeapi.org"
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置常量
EXPIRATION_DATE = 20251231  # 有效期限
REQUEST_TIMEOUT = 2  # 请求超时时间（秒）
MAX_RETRIES = 2  # 每个API的最大重试次数
RETRY_DELAY = 0.5  # 重试间隔（秒）

def get_taobao_time():
    """从淘宝API获取服务器时间"""
    url = 'https://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    }

    try:
        response = requests.get(url, headers=headers, verify=False, timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            json_object = json.loads(response.text)
            timestamp = int(json_object.get('data', {}).get('t', 0))
            if timestamp > 0:
                timestamp_seconds = timestamp / 1000
                date_time = datetime.fromtimestamp(timestamp_seconds)
                return date_time.strftime('%Y%m%d')
    except Exception:
        pass

    return None

def get_suning_time():
    """从苏宁API获取服务器时间"""
    url = 'https://quan.suning.com/getSysTime.do'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    }

    try:
        response = requests.get(url, headers=headers, verify=False, timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            json_object = json.loads(response.text)
            timestamp = json_object.get('sysTime2')
            if timestamp:
                date_time_obj = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                return date_time_obj.strftime('%Y%m%d')
    except Exception:
        pass

    return None

def get_worldtime_api():
    """从WorldTime API获取服务器时间（作为第三个备选API）"""
    url = 'http://worldtimeapi.org/api/timezone/Etc/UTC'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    }

    try:
        response = requests.get(url, headers=headers, timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            json_object = json.loads(response.text)
            datetime_str = json_object.get('datetime')
            if datetime_str:
                # 格式: 2023-09-18T12:34:56.789123+00:00
                date_time_obj = datetime.fromisoformat(datetime_str)
                return date_time_obj.strftime('%Y%m%d')
    except Exception:
        pass

    return None

# 移除本地时间获取函数，不再使用本地时间作为备选方案

def is_time_valid(date_str):
    """检查时间是否在有效期内"""
    if not date_str:
        return False

    try:
        date_int = int(date_str)
        return date_int <= EXPIRATION_DATE
    except (ValueError, TypeError):
        return False

def isValidTime():
    """
    验证当前时间是否在有效期内

    尝试从多个时间服务器获取时间，并验证是否在有效期内。
    如果所有在线API都失败，则返回False，不使用本地时间。

    Returns:
        bool: 如果时间在有效期内返回True，否则返回False
    """
    # 定义时间获取函数列表
    time_functions = [
        get_taobao_time,
        get_suning_time,
        get_worldtime_api
    ]

    # 增加重试次数，提高成功率
    max_attempts = MAX_RETRIES * 3

    # 尝试所有API，每个API多次尝试
    for _ in range(3):  # 最多尝试3轮
        # 随机打乱函数顺序，避免总是请求同一个API
        random.shuffle(time_functions)

        # 尝试从每个API获取时间
        for func in time_functions:
            for attempt in range(max_attempts):
                try:
                    current_time = func()
                    if current_time:
                        # 验证时间是否有效
                        if is_time_valid(current_time):
                            return True
                        else:
                            # 时间无效（超过有效期）
                            return False
                except Exception:
                    pass

                # 添加随机延迟，避免请求过于频繁
                if attempt < max_attempts - 1:
                    time.sleep(RETRY_DELAY + random.random() * 0.5)

    # 如果所有API都失败，返回False
    return False


# 移除测试代码，使验证过程对用户完全无感知
if __name__ == '__main__':
    if isValidTime():
        print("当前时间有效，未超过有效期")
    else:
        print("当前时间无效，可能超过有效期或无法访问时间服务器")