# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HA<PERSON>_TRAFFIC_SECRET 3e8c357f59964f40d287b2a8a29962dbddb146cfc2d2b1c5cd3516cc3f985bd0 24fb77e205f69df47db0b8d16d00921efca83486ba0f25669af42304ae913b83fe2c361adc94c6ba3c9da43094cd9dc4
EXPORTER_SECRET 3e8c357f59964f40d287b2a8a29962dbddb146cfc2d2b1c5cd3516cc3f985bd0 ae5c63486e573ff71a2548d7850028c9c3119492c42f0bafc5cf5a44d4b0fc21bf30f78685b65c427e701401e170d12c
SERVER_TRAFFIC_SECRET_0 3e8c357f59964f40d287b2a8a29962dbddb146cfc2d2b1c5cd3516cc3f985bd0 8d8856f3a905358289ddc913dc5ade89ae3b3503014d6b0d91b020e92fa7a8f8e902543c60066e2f72a126b6b3ebdf4b
CLIENT_HANDSHAKE_TRAFFIC_SECRET 3e8c357f59964f40d287b2a8a29962dbddb146cfc2d2b1c5cd3516cc3f985bd0 d74b7869ff979673f4f9e7d7dde5791b305994f0521ca61bbc1a94638fb14f5ffd37cfd57aaca850948cc30ce1d266bd
CLIENT_TRAFFIC_SECRET_0 3e8c357f59964f40d287b2a8a29962dbddb146cfc2d2b1c5cd3516cc3f985bd0 5e91c24e433175b3cfe5947263652afaebd2d108d1cd56c976b9aea5412afa58c7e94d92abaf73130c20db922f9eca50
