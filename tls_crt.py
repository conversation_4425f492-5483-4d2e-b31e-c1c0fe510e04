ss1 = '3082029e30820224a0030201020214788ee990c9b729c6b2d2bb63fd2d1ce605bb00ba300a06082a8648ce3d04030330413110300e060355040a130746697265666c79310b3009060355040b130245553120301e0603550403131756656869636c652050726f647563742043412d54455354301e170d3234303630353130333730315a170d3339303630353130333730315a30703110300e060355040a130746697265666c79310b3009060355040b13024555313d303b060a0992268993f22c640101132d7649443a39346361653631326463343134646232323939363732373637303330333031302f656e763a746573743110300e06035504031307544c532d5644463059301306072a8648ce3d020106082a8648ce3d03010703420004917fba9437808c78e64443ae4567d7fbd7cdac8388e145e476a74c804484e3c3b3186e58acb8c7097dd80a480d2f919e8e39e834c668c597485131edc21e2e06a381ca3081c7301d0603551d0e04160414f53dc9d31bfdd5e6285a5ed330867d15d67d6c7e301f0603551d2304183016801492b80155371143a9de48221cf6b9f177a3996773300c0603551d130101ff04023000300e0603551d0f0101ff0404030203a8301d0603551d250416301406082b0601050507030106082b06010505070302302b0603551d1f042430223020a01ea01c861a687474703a2f2f6c6f63616c686f73743a383038302f63726c2f301b060b2b0601040183ce3f000101040c130a50562d544c532e564446300a06082a8648ce3d040303036800306502310086afadcfb80ed5efe79fff178f906ee1569f46f20c04a90726dfbf565661199c959042a7ea388746883b208b4bc0afc4023070475fca5bbe7c1fc3db6756f10700eb64cf0579fd4033fb2f4388f0a9d2b30d9e5657552ecff73b4ff407265e9ae7c1'
ss2 = '308201d330820158a003020102020101300a06082a8648ce3d04030330313110300e060355040a130746697265666c79311d301b0603550403131450726f6475637420526f6f742043412d544553543020170d3233303431343136303330325a180f32303939313233313233353935395a30313110300e060355040a130746697265666c79311d301b0603550403131450726f6475637420526f6f742043412d544553543076301006072a8648ce3d020106052b81040022036200046f506ea9579706e9ca3a870d9c29cf8603c05d1526e8218c696e37232370e0e51037f4bdebe27890dc883815077fa028f646e1d8beea3f445b0c4f9932168bd9bcc83813424fb60811df54fa1deb31fe8f1a5dd5eb3ca58a1610e7ac0075cae3a3423040301d0603551d0e04160414089c34c80b6830dad2224c36cd538ca484b2a061300f0603551d130101ff040530030101ff300e0603551d0f0101ff040403020106300a06082a8648ce3d0403030369003066023100dd2801d4da0477dd2a63db82c08a1477fed07e421ff2ac2494e19ad02b7c500b62d6b39cfd0ae5e1b43b3a2b38474ec1023100c9c8fca9d7215ae8013be075a40c4e0da59cfa96ed2724fdecb30fc41735b79ee5f6ea35c194a622e3ccf0af67fbf587'
ss3 = '3082024b308201d0a0030201020204771cea2a300a06082a8648ce3d04030330313110300e060355040a130746697265666c79311d301b0603550403131450726f6475637420526f6f742043412d544553543020170d3233303431353033323432305a180f32303633303431353033323432305a30413110300e060355040a130746697265666c79310b3009060355040b130245553120301e0603550403131756656869636c652050726f647563742043412d544553543076301006072a8648ce3d020106052b8104002203620004558c051609faa0022083caa3268ad7e882389c0378fb29e8e6e1d99c0d5720701fdb35bf43d6e3f40b84a28fad1e23abedd28b3be9c5c579d8d03f961aebef5f8513018aeab6e11168c818d63f07df9f8789d965a6f22865b31248ac73523d26a381a63081a3301d0603551d0e0416041492b80155371143a9de48221cf6b9f177a3996773301f0603551d23041830168014089c34c80b6830dad2224c36cd538ca484b2a06130120603551d130101ff040830060101ff020100300e0603551d0f0101ff040403020106303d0603551d1f043630343032a030a02e862c687474703a2f2f63612e6d796f72672e6f72672f63726c2f46697265666c795f524f4f545f43415f54455354300a06082a8648ce3d0403030369003066023100f717f43104f774930694cb2c59f94b129a47b51444ae962ddac9f176525c82ed6999396e698cefde860f6e36f0107d9f02310094af24a6bb2cee82ca195ef3b359b3ed9d650a1297df3ca591857c94ef9b03cbb1390d176de36eee600c4257def23337'
ss4 = '308202a93082022fa0030201020214699c00fae63748eeae648f76d21c760aa1a3897a300a06082a8648ce3d0403033039310c300a060355040a13034e494f312930270603550403132052656c6174696f6e7320496e6672617374727563747572652043412d54455354301e170d3234303230323035343231335a170d3237303230323035343231335a302e3110300e060355040a130746697265666c79311a301806035504030c11546c735f446f69704f7665725f746573743059301306072a8648ce3d020106082a8648ce3d0301070342000419f2a5589c4d06006bfa4ff673527c6cecb9db4ccae8d0a15c00796b97befb971e46d4682999ba9a73ad7698be497f7e66e25ce4686de5886f031678645e9792a382011e3082011a301d0603551d0e04160414d7f156ba47e78ec5031c44868ef3c554515a1027301f0603551d230418301680141e01ae06a92325199768d05196a624c1234aff96300c0603551d130101ff04023000300e0603551d0f0101ff0404030205e0301d0603551d250416301406082b0601050507030106082b06010505070302301c0603551d11041530138211546c735f446f69704f7665725f74657374303706082b06010505070101042b3029302706082b06010505073001861b687474703a2f2f6c6f63616c686f73743a383038302f6f6373702f302b0603551d1f042430223020a01ea01c861a687474703a2f2f6c6f63616c686f73743a383038302f63726c2f3017060b2b0601040183ce3f0001010408130652542d574542300a06082a8648ce3d040303036800306502307f443d860f946fd8f8d1d0a62d3984b6863879707f8675b3681c77020b9244f45e01cd53b6c750de00d5537d286d3ab7023100fc86d99597d80ca2bb2221939ae700a41dd748664c264afb8393f734de0844eb0215701fff4e72a650061c3f7d4c6877'
# 保存第一个证书
binary_data1 = bytes.fromhex(ss1)
with open('test1.crt', 'wb') as f:
    f.write(binary_data1)

# 保存第二个证书
binary_data2 = bytes.fromhex(ss2)
with open('test2.crt', 'wb') as f:
    f.write(binary_data2)

# 保存第三个证书
binary_data3 = bytes.fromhex(ss3)
with open('test3.crt', 'wb') as f:
    f.write(binary_data3)

binary_data4 = bytes.fromhex(ss4)
with open('client_exchange.crt', 'wb') as f:
    f.write(binary_data1)