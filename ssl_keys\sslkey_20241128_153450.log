# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 084a7ccfaff3f5941ccb78989f42a86c0278c3c0899ad1edea6bb82cd649b5da 9e44eef8a807ddc8cb857f07e8dedf344da8bcb6e08fd2c4837c0705f85a72f183004239271f3d50eef4a9b4bb0267f8
EXPORTER_SECRET 084a7ccfaff3f5941ccb78989f42a86c0278c3c0899ad1edea6bb82cd649b5da 3e4a2291b70c67fdd65fe2624b28f40f41547610a4dd4b38fa08a22e7ec2b07831f795803f188dc9f9231108ec324714
SERVER_TRAFFIC_SECRET_0 084a7ccfaff3f5941ccb78989f42a86c0278c3c0899ad1edea6bb82cd649b5da c2981088a38c0e17f5c38a72cc2567e17d55928764d898ca4b4679c357d14d83034ffd2bc60397474fcd4bda466331ea
CLIENT_HANDSHAKE_TRAFFIC_SECRET 084a7ccfaff3f5941ccb78989f42a86c0278c3c0899ad1edea6bb82cd649b5da 4d5306b64c42738529ce68562110c8c07a1ab82a08a89fc2689bb4da6b92ca31f28e5a36c24bfae84e50626ea350bf21
CLIENT_TRAFFIC_SECRET_0 084a7ccfaff3f5941ccb78989f42a86c0278c3c0899ad1edea6bb82cd649b5da 52ef1acd534aa9847ba0590544f920485a577c1e950c2d191073b83c11e61de6f9fd039ca9d3dcc9d900b7e579e8b3c7
