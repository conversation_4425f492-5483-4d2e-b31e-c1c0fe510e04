# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET 2cea2322667197e7fd5f69ad120c82b840e685095839eb6829403f4f3b7da0ed d27c40ce0aa06dce566b28ff3bdfea2a950b02ada07f5a603091793d93eee1019ccace344b4fc47f71b49d4b7910db68
EXPORTER_SECRET 2cea2322667197e7fd5f69ad120c82b840e685095839eb6829403f4f3b7da0ed 9f48161a9509cfd5f103be66413e99021916b94ac8cc076d2821875ccbfa3fe4769e0b359914a60e76a558bc345ebfd8
SERVER_TRAFFIC_SECRET_0 2cea2322667197e7fd5f69ad120c82b840e685095839eb6829403f4f3b7da0ed 089ee517f27b29d09399ef0510c57393eac1b5c47591906fadc803caf0c16e09a2406da84e86d46786759380cf092fdb
CLIENT_HANDSHAKE_TRAFFIC_SECRET 2cea2322667197e7fd5f69ad120c82b840e685095839eb6829403f4f3b7da0ed 4a13852e8f3c17b3643dc80f42a089c3c327463fa9b1155822884b86a03d241dee26ed326ead11d17e074fed99b36d88
CLIENT_TRAFFIC_SECRET_0 2cea2322667197e7fd5f69ad120c82b840e685095839eb6829403f4f3b7da0ed 52e9a1844ca7f46c746ce01195b889edfec88b85a1a0698eca6fa646541de285e225c8d9b1db2eab195920d6c445e067
