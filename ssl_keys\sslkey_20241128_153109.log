# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 7845ecacacf8b1896f5606e0b3d7ed836231e6a58673ad3a11a5223d4ba5b1a3 197d627452d245908574a24a02dd2d5904018e7849819ace0a3c73967fe619cecb7e1948ba57e2b0f204939a2eb8c414
EXPORTER_SECRET 7845ecacacf8b1896f5606e0b3d7ed836231e6a58673ad3a11a5223d4ba5b1a3 672e68dce0c6b13c431807272729430911c3a7b18fe4f7e7b1936ce2f43685117ba875d3c9d2c031dca04a4361f4f2e6
SERVER_TRAFFIC_SECRET_0 7845ecacacf8b1896f5606e0b3d7ed836231e6a58673ad3a11a5223d4ba5b1a3 49eeba061e047e662544ada07d7d822c596f7cb01fe1c0e8e49ca8556a4924b05eda0aa6e60c919cc9d1d1cc5d3d21d5
CLIENT_HANDSHAKE_TRAFFIC_SECRET 7845ecacacf8b1896f5606e0b3d7ed836231e6a58673ad3a11a5223d4ba5b1a3 d01ad9b8e2946b8e22bf54e323c81564352ed2f754e38114fd82fce2b0a6612238a5a4a14fecfc1264f4905e370a4a52
CLIENT_TRAFFIC_SECRET_0 7845ecacacf8b1896f5606e0b3d7ed836231e6a58673ad3a11a5223d4ba5b1a3 29f2899585389eb85f3982893fa100356668097d06c743a237598c0621e269b0cc2fdbc66fb51fc71db72eec9d579b6d
