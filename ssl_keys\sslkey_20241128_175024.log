# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 101cbfd3d66cf62743051fae1469caf0f1fd307ef4cf0d3e7d068c0aeafffb79 117bfa01fdcec7ac3b46c5df52da44798570a8a43e86e451afebb323186a9ab3703814e34c0a6c5c025cdd5d6b8b9068
EXPORTER_SECRET 101cbfd3d66cf62743051fae1469caf0f1fd307ef4cf0d3e7d068c0aeafffb79 e70d6f501e30678022ce4419f2e58bfd7de6ac073d7544dd84dda1bf17fbae42daf01684cb60641cef037b2630856782
SERVER_TRAFFIC_SECRET_0 101cbfd3d66cf62743051fae1469caf0f1fd307ef4cf0d3e7d068c0aeafffb79 938ea29388578a3f48ff939f589b41f6d6c877f0dd9719f99ab7f6319f6d37cb3502b0f1303e338e700447ec5f82a8dd
CLIENT_HANDSHAKE_TRAFFIC_SECRET 101cbfd3d66cf62743051fae1469caf0f1fd307ef4cf0d3e7d068c0aeafffb79 be328455704b97265aabe1d5d99aca3a9e6cc41fe5573744fa51f3972c6a7916c9b05965f5601fc3bb8f7263fee37e24
CLIENT_TRAFFIC_SECRET_0 101cbfd3d66cf62743051fae1469caf0f1fd307ef4cf0d3e7d068c0aeafffb79 a89046aae8b880670a076c0c50b5c111d07ffb13873044cc6c0d9b10b9a0bdab4dace020c988fb1e4376ab7e5e3da0bc
