# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 6a74fc1c910d28de28d8912f0ed6390a13fc408c9c6a3c7e52fb0c228222e21b 1487b8b3bb0ba3f865ad6bc4c3a77598ebba6060933483fe311f23d7d775c31b671b90b12329613de68a0c83eae79984
EXPORTER_SECRET 6a74fc1c910d28de28d8912f0ed6390a13fc408c9c6a3c7e52fb0c228222e21b 632a9048870bf115590604ba9ee8edb4f379e801c4380d48d1b5f639272010beef6b2d7e8338d229087202ac55af35e7
SERVER_TRAFFIC_SECRET_0 6a74fc1c910d28de28d8912f0ed6390a13fc408c9c6a3c7e52fb0c228222e21b 7282d8607c67cde7dca629df4c16c3cb626eed088a65205682d1fd48d31883eb2e42cc981df39cd903ea03f305ff0e1d
CLIENT_HANDSHAKE_TRAFFIC_SECRET 6a74fc1c910d28de28d8912f0ed6390a13fc408c9c6a3c7e52fb0c228222e21b 506c9c95f28f3f7d2560ce502023989413d1791b7ca905563dee65598926c5f373652cc057a0c88f773362705a7f9e1d
CLIENT_TRAFFIC_SECRET_0 6a74fc1c910d28de28d8912f0ed6390a13fc408c9c6a3c7e52fb0c228222e21b ca5984c80cd7c0cd7c77898fb992b2389d160b394e81efbf79fdabbf7d4ef3b02ce5f24f95f2844f72f0c1c5cc29a5ce
