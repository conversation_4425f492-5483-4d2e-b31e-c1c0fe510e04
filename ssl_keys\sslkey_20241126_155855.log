# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET b6ca78a5c7ceb79a4a9f6f488026db09f4fb024c7f79f81281ab8bfea59152c5 d110d7c05c4d067bc70c5d34e4a1c8ddca827b5f593031818721c9e58d03c2dff58c21894306befe074febde6560c4c4
EXPORTER_SECRET b6ca78a5c7ceb79a4a9f6f488026db09f4fb024c7f79f81281ab8bfea59152c5 eef60945727c90979d189eb85bee373130e7002de92eba041030575b0bd43f6a50e8415e0505a0ff90fd4c30d1340dfc
SERVER_TRAFFIC_SECRET_0 b6ca78a5c7ceb79a4a9f6f488026db09f4fb024c7f79f81281ab8bfea59152c5 7b2e2730e04d7e6fa58369719c91938a4a39c0d452d88b144cc1661739fd81659f5ca7c70c379f2ba7785b15c33805a4
CLIENT_HANDSHAKE_TRAFFIC_SECRET b6ca78a5c7ceb79a4a9f6f488026db09f4fb024c7f79f81281ab8bfea59152c5 272c992e2c36aa836d6affee37bd12378e7d9a533bffc0b2ae4a7a0151643d08ceaed7e39d82ff510a833dea73ba4413
CLIENT_TRAFFIC_SECRET_0 b6ca78a5c7ceb79a4a9f6f488026db09f4fb024c7f79f81281ab8bfea59152c5 4e2ff662e2d39dc1799eedde64bcc4536f32f9ada6e6791ed94f68aca09e9107acfb6ca869d62e8015d93530d624ec8e
