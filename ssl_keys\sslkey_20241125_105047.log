# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON><PERSON><PERSON>_TRAFFIC_SECRET 1fbe9e64181d65d437047abe13a473e2de42f9eafb2f0064b4bc6d7b9bdba8d3 d04119170e77376f0651b61f8c03a575629d6c559940c8bcc5d8c25f7bf797a115d489001a83a5da377b9473615647dc
EXPORTER_SECRET 1fbe9e64181d65d437047abe13a473e2de42f9eafb2f0064b4bc6d7b9bdba8d3 a9ae8c4a15577be81b57affef1ad3204f15191d466cc611566074ce8bff28dd2a055e6d3a4bf8747fa83a7bc82abacc5
SERVER_TRAFFIC_SECRET_0 1fbe9e64181d65d437047abe13a473e2de42f9eafb2f0064b4bc6d7b9bdba8d3 44c0493059aff77b05c64415bfb2fae1dc7fe030f50b2d87a5a7eff8495caea6707d113b1ca152ba6811ef623e978322
CLIENT_HANDSHAKE_TRAFFIC_SECRET 1fbe9e64181d65d437047abe13a473e2de42f9eafb2f0064b4bc6d7b9bdba8d3 8babdc6c9745b160fb51c3aee4cd99d2f65f254c9929dfca17c6008abb8ad86a83f5afb0c04271fd8c2044fda309cd49
CLIENT_TRAFFIC_SECRET_0 1fbe9e64181d65d437047abe13a473e2de42f9eafb2f0064b4bc6d7b9bdba8d3 6ddae03a78d3e93c0be2e82984b99730ede9191b038609c70885479ebdbf755c91164e4fda0fadda37d7c4b30194ebff
