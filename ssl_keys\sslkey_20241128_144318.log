# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET e3cde672ee38102b49b7bfbbcd0833a30e11ba73a2f3d54510430e290b4d999c 43be7bbda10cde2470c91a3cb64d60f3cb5c0591caf3792e8512578ac3aa603acd6e8f39b7dae5fd1569ba6897158311
EXPORTER_SECRET e3cde672ee38102b49b7bfbbcd0833a30e11ba73a2f3d54510430e290b4d999c 1e77e12f070f2bc936200e70752abc31a9027795417cab00c72066d45f4f569fcf3bf0d4bb424b3a370c454fd8afaa2f
SERVER_TRAFFIC_SECRET_0 e3cde672ee38102b49b7bfbbcd0833a30e11ba73a2f3d54510430e290b4d999c db2bd98a24f70e2f0127d63a57e9b4410ad5f94868406180cc100761a9d58ed207185d386da926624ddda9ff0d569010
CLIENT_HANDSHAKE_TRAFFIC_SECRET e3cde672ee38102b49b7bfbbcd0833a30e11ba73a2f3d54510430e290b4d999c 745f6bc07566ed955d4ef9d9de83582fe13bc7d428e84e5fba4171f6e7ed715064f418d60695ebd3381ba3b60a6d361b
CLIENT_TRAFFIC_SECRET_0 e3cde672ee38102b49b7bfbbcd0833a30e11ba73a2f3d54510430e290b4d999c 7286ef8003396dad6697e2b29857ea708ffc241345e4a2eb5494ae3058df610f0ad6dc312b72629024394264a67bcb30
