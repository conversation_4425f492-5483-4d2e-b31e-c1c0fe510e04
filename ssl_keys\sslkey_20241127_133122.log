# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 2b21136fd0ddc080fb874fcb0cb835fb711de17a92bf55de9caf07d98b06970e 429cda2f0bfd9d4885d7b01d47c0861b72839fbe79e9eba7c302aebd44690dc306c3e428d9f5860dfc1890255ef12e5f
EXPORTER_SECRET 2b21136fd0ddc080fb874fcb0cb835fb711de17a92bf55de9caf07d98b06970e de1dedd4c4e7bee817686fc93e5272a01dcce46b31ad20bc614340f47d7208f229476dfa6b944c1850a952c1bc4b1ef1
SERVER_TRAFFIC_SECRET_0 2b21136fd0ddc080fb874fcb0cb835fb711de17a92bf55de9caf07d98b06970e 2ad32938c166e3b11d5d03a7f23f25c30cb18b704eed400fdd5e0e0f3e8ed1acab3a5be1f4d34f0ff4932bbe9d904420
CLIENT_HANDSHAKE_TRAFFIC_SECRET 2b21136fd0ddc080fb874fcb0cb835fb711de17a92bf55de9caf07d98b06970e 8357a3795e0ad4c4300cf8dfb6ea334b55cd9f2005242e38d7df140f03cba26cd81a9a3cd8970c7ece913a3d65fb9598
CLIENT_TRAFFIC_SECRET_0 2b21136fd0ddc080fb874fcb0cb835fb711de17a92bf55de9caf07d98b06970e d15eda08b3976e30840d6282ad1d4a1521851baa6850e3e83537d54826218ca016aeeb50b6f753ccc16beba2829df4c3
