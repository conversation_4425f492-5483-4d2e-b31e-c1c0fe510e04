# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET b402cbd3ffbb2d2ebc26744f9df51a37c8e4822f6f4ef92a88c56e95a93a4161 a2979e7b799854abdca052fcd769a354042a99479d952eaca3841832c1b74ae1b31c1bb4f195fd828091d9b9bbd3858d
EXPORTER_SECRET b402cbd3ffbb2d2ebc26744f9df51a37c8e4822f6f4ef92a88c56e95a93a4161 2236e1e0b83077ebfab0f0b790752b6f07621fcbe61a45be9e0df3b23283e873f34786b87841a9582cda697a06d0660d
SERVER_TRAFFIC_SECRET_0 b402cbd3ffbb2d2ebc26744f9df51a37c8e4822f6f4ef92a88c56e95a93a4161 855590aef2de266d653dc4f4e0435a5eeb8cc2a3304c7c7ceb191e9ce1509e9b16042c0aaff52fdba50d0b540cc631bf
CLIENT_HANDSHAKE_TRAFFIC_SECRET b402cbd3ffbb2d2ebc26744f9df51a37c8e4822f6f4ef92a88c56e95a93a4161 9f4aeb421bf449d993dd797e714be560977fefd15e3a4df0ba93b1587bac610ac9bf75a43853c18e9c6017f16fb82de7
CLIENT_TRAFFIC_SECRET_0 b402cbd3ffbb2d2ebc26744f9df51a37c8e4822f6f4ef92a88c56e95a93a4161 9ab03e8eca2a5fea641a6df26c3b0967fdf4100de88f5b5065bce00eeaeadfd4608cf3bc29fd9a3d28d94c253a59c72a
