# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 92026d4e824c6d5704d84d13fbb41b84aebcb178e77a4b754510e4b3863e814b 0e213c8ae10f6585ccd0114dcc41192c6230214302251d8f9eede7fd681d0eaeee3ae59a0f2e767390addc9259974131
EXPORTER_SECRET 92026d4e824c6d5704d84d13fbb41b84aebcb178e77a4b754510e4b3863e814b 09fced61732c3a9058b2988172dc217f63859e8826180db43024cf0767a69c5648d97db54c01043083e2527c700d6732
SERVER_TRAFFIC_SECRET_0 92026d4e824c6d5704d84d13fbb41b84aebcb178e77a4b754510e4b3863e814b 9adb50e98c64110b0a03318a383d773394f5be23d9ba97a52d126db45577aef51658fbcac25ce12cb6e3593714cf48e0
CLIENT_HANDSHAKE_TRAFFIC_SECRET 92026d4e824c6d5704d84d13fbb41b84aebcb178e77a4b754510e4b3863e814b e839baac8f91d40f198f6b3348bd8f35b0b4a38f904225451ec93662ab5ea3018e22bf08e02fdaaeff9cc6d77f067fe8
CLIENT_TRAFFIC_SECRET_0 92026d4e824c6d5704d84d13fbb41b84aebcb178e77a4b754510e4b3863e814b ece1be024c17aadb19f15a512e14e852961a36c70a5764a0840be0e1fc98d5e81fc44a21bfe6a43602ef99d8fe0688ea
