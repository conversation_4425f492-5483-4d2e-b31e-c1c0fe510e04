# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET d41adf5d1ab37d72ffab2ac1e08f8de0ad32dc553b174b713e543cfbe563850d 76668df2b9e44574f58ffb4046a11b96334dc42e2ed0da3fbe533cbefe75f5cbc58b84adc85d478292c3d97a3fab24e4
EXPORTER_SECRET d41adf5d1ab37d72ffab2ac1e08f8de0ad32dc553b174b713e543cfbe563850d 24ec2f0127542a8bec329cd05dd90978542bf7f5040e96ad795779cc54cef35ef1d301106b8560e1bb9e557faab7ce80
SERVER_TRAFFIC_SECRET_0 d41adf5d1ab37d72ffab2ac1e08f8de0ad32dc553b174b713e543cfbe563850d 1fdc56b41f0ae607ab1680e7e5a7ad081a8ec62e825f03ee2e299ff3cd122a61b76b7761020301dc68c6cea93d1d68a8
CLIENT_HANDSHAKE_TRAFFIC_SECRET d41adf5d1ab37d72ffab2ac1e08f8de0ad32dc553b174b713e543cfbe563850d 9eb08e84ec254dc00a9d8c8ba3e06895bc4b509b3dafe56c1c1ac0ccda7054a4aaa6fb59f9cb694bfad36c8292d14ea8
CLIENT_TRAFFIC_SECRET_0 d41adf5d1ab37d72ffab2ac1e08f8de0ad32dc553b174b713e543cfbe563850d 46f94a44bfbd25047cf0941de1c3dfd42720655ee07c8b0026af5e121eaeb28975f05a4a201f02c7566aa1b58e152b64
