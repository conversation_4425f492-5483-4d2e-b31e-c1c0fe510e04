# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET 4bbda0f9adba8389f91f3fe190a424304b18659a9e0e48b829c9ec10f012829e e07e804e50bbd895160815a5b10084fac6cd18802ec081b5de0459c7980f9a6ed5097c77e87588334fc5f2e2ab99f8f2
EXPORTER_SECRET 4bbda0f9adba8389f91f3fe190a424304b18659a9e0e48b829c9ec10f012829e b037c0689cf1e588eee55a3be671e64a5003f08c7ab811ae45e6a260da1e47b01eb63fb1d648732efdade759ea127cb4
SERVER_TRAFFIC_SECRET_0 4bbda0f9adba8389f91f3fe190a424304b18659a9e0e48b829c9ec10f012829e 27abf048b8671786f6aec7207fa16933bf22a8cf77f3432b7a6c21e552578fa6d3aa8d50d076fcab63a20200403c25fa
CLIENT_HANDSHAKE_TRAFFIC_SECRET 4bbda0f9adba8389f91f3fe190a424304b18659a9e0e48b829c9ec10f012829e a9ce3b77befe66f89c46285488238c0efadb24a36ecfd1af25e193f9d6d60b9c532f0430d013c7848c2e564a3279f888
CLIENT_TRAFFIC_SECRET_0 4bbda0f9adba8389f91f3fe190a424304b18659a9e0e48b829c9ec10f012829e 66b4151edbcefcc3ffe96ecebd723893bf2dd283656aefbd18ac72f8be2657670070cd00d66e56ec52ec79b7f5cd3607
