# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET c865ffa0635a74ee722934965bf6bf3c6958f86825e4e1eaf944e80b0188c2b8 7a772a580f334c851ea2e2883474b76ae7e68579b4da69c945331a1a8c1faeb7a777fbe37010cd944596074f97785665
EXPORTER_SECRET c865ffa0635a74ee722934965bf6bf3c6958f86825e4e1eaf944e80b0188c2b8 a79b13c3572f1908c7b839dafe175afb36c620e516965fe2d5b100ed5847bc1df3547c133d88a1a9298a07bd0a7df3a5
SERVER_TRAFFIC_SECRET_0 c865ffa0635a74ee722934965bf6bf3c6958f86825e4e1eaf944e80b0188c2b8 0ae440be8fa4ed7d9f3ff3de6b0b4b37ba28c5fd55c167f37ae88929f47fc770b27621dd81e8d59a8dd8d6e92755ad24
CLIENT_HANDSHAKE_TRAFFIC_SECRET c865ffa0635a74ee722934965bf6bf3c6958f86825e4e1eaf944e80b0188c2b8 fd435f44283bd860fcd233ec90739bd8cccc54cf1d72b1ed1510440efe02f1eca36778379abf3bf97183c8c64c64041a
CLIENT_TRAFFIC_SECRET_0 c865ffa0635a74ee722934965bf6bf3c6958f86825e4e1eaf944e80b0188c2b8 8ad04a6febdf5d44bf7c626980266224aff91300e224d3071fcf3ca43fcf8d47dd7f24648686f6b85a0fae9254d1a531
