import socket
from scapy.sendrecv import sr1
from scapy.contrib.automotive.doip import DoIP
from scapy.main import load_contrib
from utils.tool import *
from scapy.contrib.automotive import log_automotive
import ssl
import os
import time

from scapy.layers.inet import *
import logging
logger = logging.getLogger(__name__)
RouteActivationCode = {0x00: "Routing activation denied due to unknown source address.",
            0x01: "Routing activation denied because all concurrently supported TCP_DATA sockets are registered and active.",  # noqa: E501
            0x02: "Routing activation denied because an SA different from the table connection entry was received on the already activated TCP_DATA socket.",  # noqa: E501
            0x03: "Routing activation denied because the SA is already registered and active on a different TCP_DATA socket.",  # noqa: E501
            0x04: "Routing activation denied due to missing authentication.",
            0x05: "Routing activation denied due to rejected confirmation.",
            0x06: "Routing activation denied due to unsupported routing activation type.",  # noqa: E501
            0x07: "Routing activation denied because the specified activation type requires a secure TLS TCP_DATA socket.",  # noqa: E501
            0x08: "Reserved by ISO 13400.",
            0x09: "Reserved by ISO 13400.", 0x0a: "Reserved by ISO 13400.",
            0x0b: "Reserved by ISO 13400.", 0x0c: "Reserved by ISO 13400.",
            0x0d: "Reserved by ISO 13400.", 0x0e: "Reserved by ISO 13400.",
            0x0f: "Reserved by ISO 13400.",
            0x10: "Routing successfully activated.",
            0x11: "Routing will be activated; confirmation required."
}
nac = {0: "Incorrect pattern format", 1: "Unknown payload type",
            2: "Message too large", 3: "Out of memory",
            4: "Invalid payload length"
}

load_contrib("automotive.doip")
RED = "\033[1;31m"
RESET = "\033[1;0m"



class My_socket():
    def __init__(self, v_version, oem, ip='127.0.0.1', port=13400,
                 activate_routing=True, source_address=0xe80,
                 target_address=0, activation_type=0,
                 clientType=1, sslflag=False, ssl_version=None,
                 ssl_cert=None, ssl_key=None, ip_version=4,
                 app_instance=None):

        self.ip = ip
        self.port = port
        self.source_address = source_address
        self.client = clientType
        self.socket = None
        self.version = v_version
        reserved_oem = oem
        self.ssl_version = ssl_version
        self.ipversion = ip_version

        self.ssl_cert = ssl_cert
        self.ssl_key = ssl_key
        self.app_instance = app_instance

        try:
            if sslflag:
                self._init_ssl_socket()
            else:
                self._init_socket()

            if self.socket:
                local_addr = self.socket.getsockname()[0]
                if self.app_instance:
                    self.app_instance.init_capture(local_addr)
                    time.sleep(0.1)

            if self.socket and activate_routing:
                resp = self._activate_routing(v_version, source_address, target_address, activation_type, reserved_oem)
                if resp and resp == 'deloem':
                    self._activate_routing(v_version, source_address, target_address, activation_type)
                    
        except socket.timeout:
            print(RED+'[!] Socket Connect Failed！！！'+RESET)


    def _init_ssl_socket(self):
        # 创建SSL上下文
        ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
        if self.ssl_version == 'TLS1.2':
            ssl_context.minimum_version = ssl.TLSVersion.TLSv1_2
            ssl_context.maximum_version = ssl.TLSVersion.TLSv1_2
        elif self.ssl_version == 'TLS1.3':
            ssl_context.minimum_version = ssl.TLSVersion.TLSv1_3
            ssl_context.maximum_version = ssl.TLSVersion.TLSv1_3
        
        # 加载证书和密钥
        ssl_context.load_cert_chain(certfile=self.ssl_cert, keyfile=self.ssl_key)
        
        # 设置keylog文件
        keylog_file = os.environ.get('SSLKEYLOGFILE')
        if keylog_file:
            ssl_context.keylog_filename = keylog_file
            # Convert to relative path for logging
            relative_path = os.path.relpath(keylog_file)
            logger.info(f'SSL context keylog file: {relative_path}')
        
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # 创建基础socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, True)
        sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
        sock.ioctl(socket.SIO_KEEPALIVE_VALS, (1, 60 * 1000, 30 * 1000))
        sock.settimeout(10)

        try:
            # 包装socket并进行TLS握手
            ssl_sock = ssl_context.wrap_socket(sock, 
                                             do_handshake_on_connect=True,
                                             server_hostname=None)  # 禁用hostname验证
            ssl_sock.connect((self.ip, self.port))
            
            if ssl_sock:
                self.socket = ssl_sock
                logger.info('TLS Socket Connect Success!')
                print('[+] TLS Socket Connect Success！！！')
                
                # 验证keylog文件是否被写入
                if keylog_file and os.path.exists(keylog_file):
                    size = os.path.getsize(keylog_file)
                    logger.info(f'Keylog file size after connection: {size} bytes')
            else:
                logger.error('TLS Socket Connect Failed!')
                print(RED+'[!] TLS Socket Connect Failed！！！'+RESET)
                
        except ssl.SSLError as e:
            logger.error(f'SSL Error: {str(e)}')
            print(RED+f'[!] SSL Error: {str(e)}'+RESET)
            raise
        except Exception as e:
            logger.error(f'Socket Error: {str(e)}')
            print(RED+f'[!] Socket Error: {str(e)}'+RESET)
            raise
    
    def _init_socket(self):
        if self.ipversion == 4:
            sk = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        elif self.ipversion == 6:
            sk = socket.socket(socket.AF_INET6, socket.SOCK_STREAM)
    
        sk.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, True)
        sk.ioctl(socket.SIO_KEEPALIVE_VALS, (1, 60 * 1000, 30 * 1000))
        sk.settimeout(10)
        if self.client:
            sk.connect((self.ip, self.port))
            if sk:
                self.socket = sk
                print('[+] Socket Connect Success！！！')
            else:
                print(RED+'[!] Socket Connect Faild！！！'+RESET)

        else:
            if self.ipversion == 4:
                sk.bind(('0.0.0.0', 13400))
                sk.listen(5)
            elif self.ipversion == 6:
                server_address = ('::', 13400)  
                sk.bind(server_address)
                sk.listen(5)
            while True:
                conn, addr = sk.accept()
                if addr[0] == self.ip:
                    break
                else:
                    conn.close()
            if conn:
                self.socket = conn
                print('[+] Socket Connect Success！！！')
    
    def _activate_routing(self,
                          v_version,
                          source_address,  # type: int
                          target_address,  # type: int
                          activation_type,  # type: int
                          reserved_oem=b""  # type: bytes
                          ):  # type: (...) -> None
        pkt = DoIP(protocol_version=v_version['protocol_version'],inverse_version=v_version['inverse_version'],payload_type=0x5, activation_type=activation_type,
                 source_address=source_address, reserved_oem=reserved_oem)
        
        self.socket.send(raw(pkt))
        my_logger(pkt,1,logger)
        resp = receiver(self.socket)
        if resp:
            my_logger(resp, 0, logger)


        if resp and resp.payload_type == 0x6 and \
                resp.routing_activation_response == 0x10:
            self.target_address = target_address or \
                resp.logical_address_doip_entity
            log_automotive.info(
                "Routing activation successful! Target address set to: 0x%x",
                self.target_address)
        elif resp and resp.protocol_version == 0x03 and resp.inverse_version==0xfc:
            print('[*] ECU Support IS0 13400-2:2019')
            print('[*] Please use set --version 0x3 and reconnect')
            self.socket.close()
        elif resp and resp[DoIP].payload_type == 0x0:
            #exp: 02fd00000000000100
            if resp[DoIP].nack == 0x04:
                return 'deloem'
            else:
                print(RED+'[!] Routing Activation Failed: '+nac[resp[DoIP].nack]+RESET)

        elif resp and resp.payload_type == 0x6 and resp[DoIP].routing_activation_response != 0x10:
            # exp: 02fd00060000000d123400060000000000ffffffff
            print(RED+'[!] Routing Activation Failed: '+RouteActivationCode[resp[DoIP].routing_activation_response]+RESET)
        else:
            print('[*] May not be necessary to Routing activation')
            print('[*] Please manually set the target address')