# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET e384bafeffba50ce2f5582afe64126bb4f746daed279949e183c297980be8e0b a4878cd2e221181d2d3d04bdcf931ab7b27bcf2b03ea07e22fda30315c5334a12455369accababe042d9df48dc66c8bf
EXPORTER_SECRET e384bafeffba50ce2f5582afe64126bb4f746daed279949e183c297980be8e0b e3707be5d1edaa8841617c7e9a677b9757a492382df8dbab126c71c8477be8e5c3d89db24af70b7a2b188dce3ef5cd4c
SERVER_TRAFFIC_SECRET_0 e384bafeffba50ce2f5582afe64126bb4f746daed279949e183c297980be8e0b 1e8f3b393f7f956ba7b648caa9e05e08e0b605f0d941b071b9406f77abc3eef19cad8f246ed8f89a1e5772ad5bc96de4
CLIENT_HANDSHAKE_TRAFFIC_SECRET e384bafeffba50ce2f5582afe64126bb4f746daed279949e183c297980be8e0b e36fa4fca11e8ee9af545dbecd1d6d8fe18b186da523a9f75991b43c762a26b697d2657bdb3c082fa8d72fa9db962160
CLIENT_TRAFFIC_SECRET_0 e384bafeffba50ce2f5582afe64126bb4f746daed279949e183c297980be8e0b b9ee160a4920787bbc6a628e58c1f70f206eb681b0c889c42cec765a965dfe092e89076c85901aed83b454db4a3a96c1
