import requests
import urllib3
from datetime import datetime
import os
import time
import random
import threading

# 禁用不安全请求警告
os.environ["NO_PROXY"] = "api.m.taobao.com, quan.suning.com, worldtimeapi.org"
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置常量
EXPIRATION_DATE = 20251231  # 有效期限
REQUEST_TIMEOUT = 0.5  # 请求超时时间（秒）
MAX_RETRIES = 3  # 每个API的最大重试次数
RETRY_DELAY = 0.05  # 重试间隔（秒）

# 禁用代理的session
def create_no_proxy_session():
    session = requests.Session()
    session.trust_env = False  # 彻底禁止使用环境变量代理
    return session

session = create_no_proxy_session()

def get_taobao_time():
    """从淘宝API获取服务器时间"""
    url = 'https://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    }
    try:
        response = session.get(url, headers=headers, verify=False, timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            json_object = response.json()
            timestamp = int(json_object.get('data', {}).get('t', 0))
            if timestamp > 0:
                timestamp_seconds = timestamp / 1000
                date_time = datetime.fromtimestamp(timestamp_seconds)
                return date_time.strftime('%Y%m%d')
    except Exception:
        pass
    return None

def get_suning_time():
    """从苏宁API获取服务器时间"""
    url = 'https://quan.suning.com/getSysTime.do'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    }
    try:
        response = session.get(url, headers=headers, verify=False, timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            json_object = response.json()
            timestamp = json_object.get('sysTime2')
            if timestamp:
                date_time_obj = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                return date_time_obj.strftime('%Y%m%d')
    except Exception:
        pass
    return None

def get_worldtime_api():
    """从WorldTime API获取服务器时间"""
    url = 'http://worldtimeapi.org/api/timezone/Etc/UTC'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    }
    try:
        response = session.get(url, headers=headers, timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            json_object = response.json()
            datetime_str = json_object.get('datetime')
            if datetime_str:
                date_time_obj = datetime.fromisoformat(datetime_str)
                return date_time_obj.strftime('%Y%m%d')
    except Exception:
        pass
    return None

def is_time_valid(date_str):
    """检查时间是否在有效期内"""
    if not date_str:
        return False
    try:
        date_int = int(date_str)
        return date_int <= EXPIRATION_DATE
    except (ValueError, TypeError):
        return False

def thread_worker(func, result_holder, index):
    """线程执行函数，存储结果"""
    for attempt in range(MAX_RETRIES):
        try:
            res = func()
            if res and is_time_valid(res):
                result_holder[index] = True
                return
            elif res:
                # 时间无效（过期）
                result_holder[index] = False
                return
        except Exception:
            pass
        if attempt < MAX_RETRIES - 1:
            time.sleep(RETRY_DELAY + random.random() * 0.5)
    result_holder[index] = False

def isValidTime():
    """多线程调用时间验证，任一通过即成功"""
    time_functions = [get_taobao_time, get_suning_time, get_worldtime_api]
    result_holder = [None, None, None]  # 存放线程结果

    threads = []
    for i, func in enumerate(time_functions):
        t = threading.Thread(target=thread_worker, args=(func, result_holder, i))
        t.start()
        threads.append(t)

    # 等待线程完成 或者 任何线程结果为 True 就提前返回
    while any(r is None for r in result_holder):
        if any(r is True for r in result_holder):
            # 其中一个验证通过，立即返回 True
            return True
        time.sleep(0.1)

    # 等待所有线程结束
    for t in threads:
        t.join()

    # 没有一个线程返回 True，返回 False
    return any(r is True for r in result_holder)

# 你可以测试这个函数
# if __name__ == '__main__':
#     if isValidTime():
#         print("当前时间有效，未超过有效期")
#     else:
#         print("当前时间无效，可能超过有效期或无法访问时间服务器")
