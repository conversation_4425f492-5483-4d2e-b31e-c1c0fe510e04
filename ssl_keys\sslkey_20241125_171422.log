# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 8f2fcf7d1cf472553d26676090882bc04546ce5d5031f6b0a037b37068ca3cf3 380526969b7dd4b46bbc8a01c9d48e72f29ac70da4a96982b63f550f08ae2c45adfae9b33633b5d56ce377789f3a8ade
EXPORTER_SECRET 8f2fcf7d1cf472553d26676090882bc04546ce5d5031f6b0a037b37068ca3cf3 65a9b1f516527524f6d6a2689a74e1859e2c9cf17b88bd792fab377a6d99e091ae6c03ddaf743a6ed69a8c4a362e79af
SERVER_TRAFFIC_SECRET_0 8f2fcf7d1cf472553d26676090882bc04546ce5d5031f6b0a037b37068ca3cf3 553922ecf9f411addb720f48fee78e9938d0c76fd725a9ed979bc567a708447ab42a8d32e4735c1b2cb00daf8aeb69f1
CLIENT_HANDSHAKE_TRAFFIC_SECRET 8f2fcf7d1cf472553d26676090882bc04546ce5d5031f6b0a037b37068ca3cf3 815e674d3f01950146c58e518112c1bbe3ffbe2774bc65d67819c1bbf8a5bfaea5b0eb240251ec6fc83ae0a45b9a7ea6
CLIENT_TRAFFIC_SECRET_0 8f2fcf7d1cf472553d26676090882bc04546ce5d5031f6b0a037b37068ca3cf3 824e0e843c65a07b59b20fae78223b5261ee3b40e18098ca2681cc0d664639109eb429d5ee43a78e67a157af65fc9848
