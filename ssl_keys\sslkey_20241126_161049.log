# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON><PERSON><PERSON>_TRAFFIC_SECRET acaf1e4640d8fcf9041acddeadf29098045587f3733c9f922431bf01b53dc1bc 13c15641e054099c1dbcec11293c54f92d46a0862125b2a85443576d0b572833355c4d00dd964b81e2b8c2b6b59e6f37
EXPORTER_SECRET acaf1e4640d8fcf9041acddeadf29098045587f3733c9f922431bf01b53dc1bc 9194c11bc1d2c9d54b8be5cd0ff4e734a7bd55f5cea9ed7fd8fd3d3f8d9be835d84d5803e3646ed342e8e1aa8984e29d
SERVER_TRAFFIC_SECRET_0 acaf1e4640d8fcf9041acddeadf29098045587f3733c9f922431bf01b53dc1bc 4598633800eae133e56dc01554634a0aa7d653f77a5ed11250ff43da7468511c44b31289327494c9e5631f12ecbb972a
CLIENT_HANDSHAKE_TRAFFIC_SECRET acaf1e4640d8fcf9041acddeadf29098045587f3733c9f922431bf01b53dc1bc d4b715e2046e57b141fe351f6d3160b70e42011f1c09b8a7ebf2495aa50906844201587fc3866b691619aef20bcc2b4a
CLIENT_TRAFFIC_SECRET_0 acaf1e4640d8fcf9041acddeadf29098045587f3733c9f922431bf01b53dc1bc 1ed6e33e000cf23244a8b33232ac00bb5815672186f6364c0db81ebbc24697ba54b9831ef99c4897e3085ccdc5039968
