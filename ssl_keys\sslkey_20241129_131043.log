# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON><PERSON>KE_TRAFFIC_SECRET 60a92013050f51c4781ab3d3398839eedccb17135ccd54a105bb36357b1534fa 82387b34f458c6f0a8bae0db162b81e798410e2d1ab6fd1fd7d854b11b300eab7757742f23e234e38c907381d2965100
EXPORTER_SECRET 60a92013050f51c4781ab3d3398839eedccb17135ccd54a105bb36357b1534fa fd7c09946c53095a5bcadbdbb4fe9eabc1914acecf141a9ba2d2e05ca56717d516dcda61dc39777f1e88cb1d5c638bb2
SERVER_TRAFFIC_SECRET_0 60a92013050f51c4781ab3d3398839eedccb17135ccd54a105bb36357b1534fa 4b5ab71c31fcf0ada1c61f5dcaaaea4a863ecfaa7d06c2b74c180f178e398ef479ca6689bd22cbf2191ac969bbfffceb
CLIENT_HANDSHAKE_TRAFFIC_SECRET 60a92013050f51c4781ab3d3398839eedccb17135ccd54a105bb36357b1534fa 6a25aa1beb08865683ed78afa7e413dd75e52658946bbd7f51e071d0b003f93c8741a50033cf6c5d0f687bc920899c8a
CLIENT_TRAFFIC_SECRET_0 60a92013050f51c4781ab3d3398839eedccb17135ccd54a105bb36357b1534fa a69618a808154ee0c951b7ffa364eab154167d40bb88e306b5e543436365a55ab803a2891bfa77142646b7ebae0cf024
