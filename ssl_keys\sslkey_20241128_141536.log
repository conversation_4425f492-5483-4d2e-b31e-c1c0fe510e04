# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON><PERSON><PERSON>_TRAFFIC_SECRET 57fd5f9de601986d8a8e42ffc84dfb6ccefbcc7d293738e0a0c8ad7bd25393fb 34649e044638b41d277be4e1db4bae16b56afc83eac2f2e2399e9f7f061e162821be57cb6432b635150e10becbd98244
EXPORTER_SECRET 57fd5f9de601986d8a8e42ffc84dfb6ccefbcc7d293738e0a0c8ad7bd25393fb 244c85dd25063f27fd73b84fd32e5b746653e331f9cc3aa35ae2b75de74f9072c769bee8d13e903e85861f016254546f
SERVER_TRAFFIC_SECRET_0 57fd5f9de601986d8a8e42ffc84dfb6ccefbcc7d293738e0a0c8ad7bd25393fb de35765323e9972bb8123bb66930a880f696c11bf64691fe0badd7f91592c95c35edced5e081969b701f09d4e9bc0736
CLIENT_HANDSHAKE_TRAFFIC_SECRET 57fd5f9de601986d8a8e42ffc84dfb6ccefbcc7d293738e0a0c8ad7bd25393fb 97ea00123e1dee8f21c6710da161e4cdb48bd56c28f874eda786be1b2c5a721ead2297363ccfecb0cae3c6566e12091d
CLIENT_TRAFFIC_SECRET_0 57fd5f9de601986d8a8e42ffc84dfb6ccefbcc7d293738e0a0c8ad7bd25393fb 3ba8d59e718436282a25282181587c9d9d623e92b00245b7e8951e6e2d1841229ba8f27f2f64d4825c4b16feefc91798
