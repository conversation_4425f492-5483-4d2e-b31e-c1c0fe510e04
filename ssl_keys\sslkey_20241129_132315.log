# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 0ff3cfe567234e0e141837fa06252b3989a8fb5a983140855f57013a6cbf9fc6 c36f8edd47bf5a45bce7091fd8df449482e03d2ef60cf8043d0153be2ee8b06419a9da7a51823354570c9f48f77e3093
EXPORTER_SECRET 0ff3cfe567234e0e141837fa06252b3989a8fb5a983140855f57013a6cbf9fc6 72424a828f84055350fc07f3d670abf99a9f1672f1206a12f180d616f11fe262a4a29d2564582eed5751077c30cee687
SERVER_TRAFFIC_SECRET_0 0ff3cfe567234e0e141837fa06252b3989a8fb5a983140855f57013a6cbf9fc6 a38b63ae9d19dabf7e7d34f275130bd2cc165729a4d196bc1a1c426fa8cd3b6d8e5cf80bdad871801849a0fe84ef0a5e
CLIENT_HANDSHAKE_TRAFFIC_SECRET 0ff3cfe567234e0e141837fa06252b3989a8fb5a983140855f57013a6cbf9fc6 c79d07168f0482fa9dc75df977a24f38bf319313352a5f3b963acdcd91f20a3d5772d85e31bb495ece1fbfa0a5e36adc
CLIENT_TRAFFIC_SECRET_0 0ff3cfe567234e0e141837fa06252b3989a8fb5a983140855f57013a6cbf9fc6 51ec33c318bf041f56b2afc05347cf19c36cd331b2d0e736192de303addde09f6936d7e3823951bedbeebdcae039ed52
