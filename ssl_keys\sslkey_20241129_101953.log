# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET bf2e6cf8c35a0e577c56d86106019d12b20b50b221f41007536a12fd037ee616 cdeb5cc93a382be7dd8feebd5406c44b2b6bc62531c5626cf8184dc498904228431c7d8402e5dddb50b2a90018519474
EXPORTER_SECRET bf2e6cf8c35a0e577c56d86106019d12b20b50b221f41007536a12fd037ee616 66dd593c54c721316152ae32d04447b1cb6b153ac110fe98c7e7f25e065a2f3a40f8b7dcc55ba0dc1c13880e45661632
SERVER_TRAFFIC_SECRET_0 bf2e6cf8c35a0e577c56d86106019d12b20b50b221f41007536a12fd037ee616 c2b6bd20f0c3f14692712c9457e9a81f8bfb6d2b4ace466e82db80c27d4a947d8c6203e855900db7996d469d1e71ee17
CLIENT_HANDSHAKE_TRAFFIC_SECRET bf2e6cf8c35a0e577c56d86106019d12b20b50b221f41007536a12fd037ee616 a5f1a5b8f22cd81b7f2b2b5ebe93ed85589c9ef4657980bd76ae37425fa0aae33649e50176803c0e91480061732a8fd1
CLIENT_TRAFFIC_SECRET_0 bf2e6cf8c35a0e577c56d86106019d12b20b50b221f41007536a12fd037ee616 9c4e31ed58648b1cdd9634eacab723b8a2243e67da4d0d7140c5f3ca560cec651247bfe95affcf62c9e83e2fed825812
