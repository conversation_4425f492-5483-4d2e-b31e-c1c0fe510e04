# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET 39a5c0620a10f8f065d2b4f71831c494a95bd3b337e10e38b1ead46925d6246c a233f5bd78f045347a5318dbe92ee888e5c83d7b7111a151807fea75c779f5b62ff8c338f1e480707fa4bd8ce137e874
EXPORTER_SECRET 39a5c0620a10f8f065d2b4f71831c494a95bd3b337e10e38b1ead46925d6246c 9c5b2e9aaf0775e14352a28ebff3e8d5c3f3cacc8774a50f39dbf7e8dfa4fa8c6b8e406d7348aa36bfe1d5287cf37cc6
SERVER_TRAFFIC_SECRET_0 39a5c0620a10f8f065d2b4f71831c494a95bd3b337e10e38b1ead46925d6246c c33d2a5430575a3f8a5e5ba69cf9eb9d44a69fa9ee6c47b663f1b3f79e23554687d1bad97e0cfa1fe4dc64e3369f85aa
CLIENT_HANDSHAKE_TRAFFIC_SECRET 39a5c0620a10f8f065d2b4f71831c494a95bd3b337e10e38b1ead46925d6246c 6edaa102259ef35cbd8c3ec45d445268beb934323e18f9d86d12cf53262b9be24875712eb42dfebd81584cf429ffb28e
CLIENT_TRAFFIC_SECRET_0 39a5c0620a10f8f065d2b4f71831c494a95bd3b337e10e38b1ead46925d6246c 488ef9dafc27730d92cc3976eca75cd6cc293bef5e12518a6acbda115af42af8260952290f9f35332b87711548e42849
