# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET f0d524bb3cbb7518e3b533eb4f2337ad46b9a9feb71b34dd9b836996b784caea 308b5899dcf38393fab4604771f7273942f496e065068e57254f0d84936f474cf935c03002827f153724b14f9d3833a8
EXPORTER_SECRET f0d524bb3cbb7518e3b533eb4f2337ad46b9a9feb71b34dd9b836996b784caea 8b10af3caae73866fbb7b4f5fc9783eb6514382f0018dedffc49e8b90057024eae07dd0b7ae2bfc7f145e52bb18a7b89
SERVER_TRAFFIC_SECRET_0 f0d524bb3cbb7518e3b533eb4f2337ad46b9a9feb71b34dd9b836996b784caea 80cbb26f341aa32c304a563cff7ff1311f92126be3e552fdd8ba8ac40dbec953077a121cb02c5baba46459ea8a1b3d15
CLIENT_HANDSHAKE_TRAFFIC_SECRET f0d524bb3cbb7518e3b533eb4f2337ad46b9a9feb71b34dd9b836996b784caea 6ae781bff3a740f1be0eab2782737db1e4cd55e53e399fecc8c20ffdc21a30b69b380680ebe8a2bae40b09992e634266
CLIENT_TRAFFIC_SECRET_0 f0d524bb3cbb7518e3b533eb4f2337ad46b9a9feb71b34dd9b836996b784caea 4118c3715775d3a4cab58d83543c902b914e068b370e710f0a2547a43397fd33ff2b0100a80452b225197f9bcb5b675c
