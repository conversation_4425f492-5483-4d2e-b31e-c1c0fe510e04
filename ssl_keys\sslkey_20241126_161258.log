# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 5296c6e44ef933194f4f631e2172523375c8e51fdb2e587e4bd074041297a7c0 04987ad8341df34af6f407fd97c4f68f3f4959fd96581795df5eb4632db360737e3db87938466cd89b1aef8fe16d6bd9
EXPORTER_SECRET 5296c6e44ef933194f4f631e2172523375c8e51fdb2e587e4bd074041297a7c0 d4adea5ac1912507d6284d6f90fc0baea7a2ee2edd10db1e3f54ab7ecaff4949bacdfca1fd57152955de66d374a997df
SERVER_TRAFFIC_SECRET_0 5296c6e44ef933194f4f631e2172523375c8e51fdb2e587e4bd074041297a7c0 d0bd74f95f89bf00a3decf386de889b4a9ccdec0d51ded0c24bdb0db0e04f9179dce1b64b70a0fda257c7103ba470b3d
CLIENT_HANDSHAKE_TRAFFIC_SECRET 5296c6e44ef933194f4f631e2172523375c8e51fdb2e587e4bd074041297a7c0 122c54e1953416f845f69d52ede01850922d04cfbd7dd79c2dfa5cd66fc9efa1427a01bd4fb11c1274bcc288fa13c035
CLIENT_TRAFFIC_SECRET_0 5296c6e44ef933194f4f631e2172523375c8e51fdb2e587e4bd074041297a7c0 bb1eb7ee2aa12ca329f8481772dc109ad76151f3206c5a8c3d149c659aa103541e36a285422d68121df0d676028c00fb
