# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 24e7e07faaff0d757f171470b2121c5442a43bbc193d22d0810408b1cebfe65e 7b87bcb307a55f1dceb35660104c0c56f7442e9dcfb8d71b99647a5b0cd20ff29d10fd5028b3bebd24ae09bb85066950
EXPORTER_SECRET 24e7e07faaff0d757f171470b2121c5442a43bbc193d22d0810408b1cebfe65e 314383f86c65743344460c8c01de42ad16e0db5a60a2b47d6374a7c98e707e40d40e9efcb6c0f30377d9c945d5b428ac
SERVER_TRAFFIC_SECRET_0 24e7e07faaff0d757f171470b2121c5442a43bbc193d22d0810408b1cebfe65e e83e95b925f1b8d14fa1faef7f0f57bbf85f02d61e494fa30803cc78413d0756f1e8f1dba6b7cf749a9918ad273a3805
CLIENT_HANDSHAKE_TRAFFIC_SECRET 24e7e07faaff0d757f171470b2121c5442a43bbc193d22d0810408b1cebfe65e 1655c4a01d0eb2204d165451692dc931e82d3176097e4da50500dbe1831c8cbc9a47a9d1cebda3d4a74e469a3e5cc5d2
CLIENT_TRAFFIC_SECRET_0 24e7e07faaff0d757f171470b2121c5442a43bbc193d22d0810408b1cebfe65e 9ffce1de0cf62a16854e782c6b11ad1ca7695939ea05b5ecec55fe4151f58327f877bf2f5cb6141e6776297f881268a5
