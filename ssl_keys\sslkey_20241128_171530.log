# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET fb95abacacbf49f984c12c704e4e1fb5b2fc4e609051f38a15c45c45de1193ea be1f8593b1de496198e1f8f0a6f42f54dfc83ba4248276dd31feefccf76cb78cb5448ff2054cb1df1fab3399fc3f9df9
EXPORTER_SECRET fb95abacacbf49f984c12c704e4e1fb5b2fc4e609051f38a15c45c45de1193ea bd2ffdd8390acd4755c2fff697e12b8d7927a6c6af5c7ce4fe84504a025a4afe5d4a6e1f2fac4c2b572e77e4de8df4c7
SERVER_TRAFFIC_SECRET_0 fb95abacacbf49f984c12c704e4e1fb5b2fc4e609051f38a15c45c45de1193ea 1bfc6bd8e1b2d25d1ed7913621e43c48e86ab6395e49cde7f754234b22ae0c1822f1ca84ccd57488942d2aee9ae76dc9
CLIENT_HANDSHAKE_TRAFFIC_SECRET fb95abacacbf49f984c12c704e4e1fb5b2fc4e609051f38a15c45c45de1193ea 8de61da00b427eaf11aed735ef84fef5c5af036e797b0ad258cb776b58e9605b7ecb8a176e78938b20640c72d280a88c
CLIENT_TRAFFIC_SECRET_0 fb95abacacbf49f984c12c704e4e1fb5b2fc4e609051f38a15c45c45de1193ea 696f060427379bb8fc69ba12ac1ac2ee93c3024d15b5eba0ae86ecea7fae70e124b16c271b0103b271dc1978ac6838ee
