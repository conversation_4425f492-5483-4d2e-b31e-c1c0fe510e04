# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HA<PERSON>_TRAFFIC_SECRET 6bb0a32525731e4aef003e1fb11dd1407c1d557e21f0a36a1040a28646255636 9de192f6b4e3b62834c73f25b1b539bc22cebddb9d3df8279771f95b2b88b7940cda56966f154c6f7d7713777ee28de0
EXPORTER_SECRET 6bb0a32525731e4aef003e1fb11dd1407c1d557e21f0a36a1040a28646255636 5f8db48f86a7c4089f1f7afc506679a4f20d966683c4494f18810600f6972cbb6162da1df765d4ef4d4cfe79c4bf51c5
SERVER_TRAFFIC_SECRET_0 6bb0a32525731e4aef003e1fb11dd1407c1d557e21f0a36a1040a28646255636 e5fec9863c71843934d19f771ab7a8d5debac17bb08fe9d1ede1931941f01e8804a8d0175fcd5b15dd4df22a2a807195
CLIENT_HANDSHAKE_TRAFFIC_SECRET 6bb0a32525731e4aef003e1fb11dd1407c1d557e21f0a36a1040a28646255636 f36877a474a7d1b463c5d1c9cc4f82b46bbee660db5bf3b4dbe0c04043b3abb46f992dc46ec7552972cb33631a9a42ca
CLIENT_TRAFFIC_SECRET_0 6bb0a32525731e4aef003e1fb11dd1407c1d557e21f0a36a1040a28646255636 fbe5045f585e5f0423867b812e202b2f1164c9e22442d355b0bb12b43393be57f7b65ac71c9159734a29317a59783218
