# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET b1f842bc9a7f6cf58c18762165a750d5101a6d383444a1e6db2fff9e3ec197f2 3654acc67d0ac04481f71fabed0729769001ca290038a056e3376fb1dfb24345346a730ecf0327d8740fb45c67c48a6a
EXPORTER_SECRET b1f842bc9a7f6cf58c18762165a750d5101a6d383444a1e6db2fff9e3ec197f2 0098e08b38c3a758565e7d95f746ae0069da7f818e3b95febba3209994370fed7455681ddacfe522f29f31e746f68f00
SERVER_TRAFFIC_SECRET_0 b1f842bc9a7f6cf58c18762165a750d5101a6d383444a1e6db2fff9e3ec197f2 99cd717634a97b74f34dfcd237510e3d0154dfeb9658090ced43903c76f842d20ef6f75d8480f2f08832e4a0931354ed
CLIENT_HANDSHAKE_TRAFFIC_SECRET b1f842bc9a7f6cf58c18762165a750d5101a6d383444a1e6db2fff9e3ec197f2 5dff8d7baf6542588cb5514363e50aa35891ac5d1b8bfe4b8745846abb274bac440efdbbede37bfa6ea8295f48b91917
CLIENT_TRAFFIC_SECRET_0 b1f842bc9a7f6cf58c18762165a750d5101a6d383444a1e6db2fff9e3ec197f2 03ef989aa9f21908868cd9e79fc01662d9d066a458fe31e7de4e423b1736a5f8514d2f276cecf72d5f284436a12a066f
