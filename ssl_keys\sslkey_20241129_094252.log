# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 40cdf2fd4395c5ffb9dea4b36b20cdbd280ec6399d70daf0595ea199145a2a5c 64c55f82f0e630149d6c350981450a496e8e1d5e5510c5bab1518525e1b0c1a285afc4edec776234a1af5327ed4008de
EXPORTER_SECRET 40cdf2fd4395c5ffb9dea4b36b20cdbd280ec6399d70daf0595ea199145a2a5c 50a7b0b91c5e0bcfe148a50555c6c806f38f19a4b1fa143113737c03f894231f7c7992d8062e9c5dddfb3e85d80877b4
SERVER_TRAFFIC_SECRET_0 40cdf2fd4395c5ffb9dea4b36b20cdbd280ec6399d70daf0595ea199145a2a5c eaa172c160c82596f5db2cd626012e3e5e61d1d54d49ba54a060728f8fcb3abdb3d93411ac5a4be5e48526988bf1c34b
CLIENT_HANDSHAKE_TRAFFIC_SECRET 40cdf2fd4395c5ffb9dea4b36b20cdbd280ec6399d70daf0595ea199145a2a5c 4016a3f6244b7b0826e7f3d9e786a8ea4ee172fc613dd03bcd638a8fe7d5328de01ca3c52636b45d4d8470b4f561a241
CLIENT_TRAFFIC_SECRET_0 40cdf2fd4395c5ffb9dea4b36b20cdbd280ec6399d70daf0595ea199145a2a5c 7a991d0e143cdfddf905b4206ef7cc5d9e06f75df0d2a7f27ee57f44d07d645dca069d412521d562abb86672e0474431
