# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON><PERSON><PERSON>_TRAFFIC_SECRET 3302988024de30d6b486f730f5bd243a03aba3c4ed621cf1aa55d19c2fa50224 2eb92c8b9a653259447a96984cd59ba34708931de2c3d9e14c34d5d4ebe73bfc6285bc73118d24daa1de3cf238af4132
EXPORTER_SECRET 3302988024de30d6b486f730f5bd243a03aba3c4ed621cf1aa55d19c2fa50224 72ec08a9849091a6e1b88827a6b5e84ec68f426cf67cbae95af73023a5c32e9135f12365de3978a35141cced3f304364
SERVER_TRAFFIC_SECRET_0 3302988024de30d6b486f730f5bd243a03aba3c4ed621cf1aa55d19c2fa50224 ea2b950dc233370e85ac6cbfcdee9e2e92d1b730405bec381262ce593060b50ce0513cde43e7fc32da9686075a5b0267
CLIENT_HANDSHAKE_TRAFFIC_SECRET 3302988024de30d6b486f730f5bd243a03aba3c4ed621cf1aa55d19c2fa50224 490c4edbbea5808b86147aee76556804f8629298a30b6699980f3ba805606b403baaf90c1cc29a7c4d64be1a3a43de62
CLIENT_TRAFFIC_SECRET_0 3302988024de30d6b486f730f5bd243a03aba3c4ed621cf1aa55d19c2fa50224 52bbbbbed581c195c2a9a616550f9530646e3fc423b3283c11fd6d85f4d3bbe0e1e24039115dfa7bd3ecdd7c38bac627
