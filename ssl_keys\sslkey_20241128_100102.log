# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET dff18b7f33f3e304528693f6a7a7d568292d3c29747a6ee5030cfd28997fab4d 2a4e40a8c9efb16b3e39293e93aed50d3f9501a248571ed4e82891f738ff4a153ef13378eae03d2e107e153fef6c58bd
EXPORTER_SECRET dff18b7f33f3e304528693f6a7a7d568292d3c29747a6ee5030cfd28997fab4d 5d1a4832b073d7d57e6723b5eba23b2f740fba4893188ffcaf17044b7c693fe0a9cc6b1cff41892adc46b0cc467e9bf0
SERVER_TRAFFIC_SECRET_0 dff18b7f33f3e304528693f6a7a7d568292d3c29747a6ee5030cfd28997fab4d 3d2593dd74526d876e680884e6e60e3526bf93891e6c89cda4c22e09edbdb7279725e6a023966956f828100775c9c943
CLIENT_HANDSHAKE_TRAFFIC_SECRET dff18b7f33f3e304528693f6a7a7d568292d3c29747a6ee5030cfd28997fab4d cc57c6005f064a5ccc90660c686a09635d4887aeaff19f396fe0f767895affbcd78516da2bf2a882cecc29c5b21ef0ae
CLIENT_TRAFFIC_SECRET_0 dff18b7f33f3e304528693f6a7a7d568292d3c29747a6ee5030cfd28997fab4d 33cf3b7ff65409ab52399720a9950e3fdfed8584ce54fbaf3fc39de1b2918fd9cd64df5529eb6845cc123802c0cff521
