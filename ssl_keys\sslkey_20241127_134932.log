# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET ffe57cf32cf223af3e0dfbbaef7fa37cfd6902e2acc09fa7d481eb11c0808c1c 6bce6e9cf56883b3bc73491b391ca3350bc066585aa1cc878561aaf972011266af7e06ee2467379c41c86b81a21704a0
EXPORTER_SECRET ffe57cf32cf223af3e0dfbbaef7fa37cfd6902e2acc09fa7d481eb11c0808c1c 4ed0afbad47d8dd5dbb0c0da84bc2ad9297cea890e7a9254d5df8cf6066c3388b2cbadcbb8933cd1db03b9c030461c96
SERVER_TRAFFIC_SECRET_0 ffe57cf32cf223af3e0dfbbaef7fa37cfd6902e2acc09fa7d481eb11c0808c1c 50d77381e8747b05d32a4c0ac1f5ced21e8adfa072e7b29952c074edea233d6df5a7850b871473dcf42796ffa4f357e3
CLIENT_HANDSHAKE_TRAFFIC_SECRET ffe57cf32cf223af3e0dfbbaef7fa37cfd6902e2acc09fa7d481eb11c0808c1c 1366ac899f344b5c01ff36096f420bdf61694c003abef714344db6119f39759421c8919fc137672da87fbd0aec1cfae9
CLIENT_TRAFFIC_SECRET_0 ffe57cf32cf223af3e0dfbbaef7fa37cfd6902e2acc09fa7d481eb11c0808c1c a39ba51b84e0a644d4155284121af2a62cc7ef7263cf1e89e43305e0583c6c2f5ebbe9e7c6542bb14ee91d3abb85e11b
