# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 7c778bc18d2ae61e188174e4eae23cd37cb2f67063a30e8cd4a9215551c4e8ce a660b69017f10c170a5a912acc230917098e741b5c7c3170f8cc061789d50f22bff3c0bf0c609ffc39b07b7af330e29b
EXPORTER_SECRET 7c778bc18d2ae61e188174e4eae23cd37cb2f67063a30e8cd4a9215551c4e8ce 25e3bb5b98e4c5f67640d6ae4a2974362bdd964ba55526896b918cbbe5193bab060e575b51998196f41f2449c076be35
SERVER_TRAFFIC_SECRET_0 7c778bc18d2ae61e188174e4eae23cd37cb2f67063a30e8cd4a9215551c4e8ce 2b9bea3ed97b543c4e9f4da6fd1b87be9138123f9416e92d0f0f8d582c9c6c985453242ffc6a99046998d7ba9eea0b08
CLIENT_HANDSHAKE_TRAFFIC_SECRET 7c778bc18d2ae61e188174e4eae23cd37cb2f67063a30e8cd4a9215551c4e8ce ea409b891608951cfc75eacaeb839bd9016437e67d3ade782157242a4c2ea5010a99ef1b0f6b029d958b6e9372870522
CLIENT_TRAFFIC_SECRET_0 7c778bc18d2ae61e188174e4eae23cd37cb2f67063a30e8cd4a9215551c4e8ce 00cc840d9af1c040b0f3d670dc3527578b3fb88ddf9f0da0c3f12feee318592c82e027701577c02bcbab8c93413ed25f
