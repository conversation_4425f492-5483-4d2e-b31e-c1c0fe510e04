# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 67c165019a581ba5ed99f424b884a05132ce279df418ee31861083346eda066a adfdbff4f29c0d346fee853d0a5730b58baa7b7ff09da68f8f4ceb6190e033b01f362c45dde629f491ce6e54e5de6db7
EXPORTER_SECRET 67c165019a581ba5ed99f424b884a05132ce279df418ee31861083346eda066a c9e7bd5f76c1d1ea7ad85f94d8c5d3d89b83d8b1b9ef6d3e88182069ecae3aa59763306d4f108abb3824d787ff0c442d
SERVER_TRAFFIC_SECRET_0 67c165019a581ba5ed99f424b884a05132ce279df418ee31861083346eda066a 8cb1dd245c76949c4092e13c0232ea16a1dd674451d90433080fcc072eca6cc8f455903a8f107b3f42ac46a3d004db27
CLIENT_HANDSHAKE_TRAFFIC_SECRET 67c165019a581ba5ed99f424b884a05132ce279df418ee31861083346eda066a 234b2f8ecb181ff7f5fab620e75c03f0ffd08298cdef717ca7e4e3a65462ebd9ddd9b593697adbfbd17bf926764e5823
CLIENT_TRAFFIC_SECRET_0 67c165019a581ba5ed99f424b884a05132ce279df418ee31861083346eda066a 112adcc8594aa2fd8dadb26a24242e53e2c6e4debe9438532b58914829c4ae59ba97bc66413fbc3d02e3da053bf935a7
