# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET 7c89abb4c15b9439c95cd9acf9fb9d356ba8d0df3fe08e223b320c4284c29dab d91f4f6635f1e0d6224fd93c8bf679e77863c58fa1f1513485de8ccde7fadc2af4eb43417310f2696de589eb549050ff
EXPORTER_SECRET 7c89abb4c15b9439c95cd9acf9fb9d356ba8d0df3fe08e223b320c4284c29dab 61061b632b2581f56bedd53b09f38e00fe088d6af95833a9b2f9a0c88d444ac224815cdacdd74356a3eb419d3e69fb1a
SERVER_TRAFFIC_SECRET_0 7c89abb4c15b9439c95cd9acf9fb9d356ba8d0df3fe08e223b320c4284c29dab f48b1a938e6359762fe5668bb4d8ff98a3180f1269de6f51f865616f5dcd3991441c1daac308f719682e860f67b1be3a
CLIENT_HANDSHAKE_TRAFFIC_SECRET 7c89abb4c15b9439c95cd9acf9fb9d356ba8d0df3fe08e223b320c4284c29dab f5e14672be1608f3612ba32a26a196f90d663bd3e7398b3e22d734053a9ff48f143727d78152b004c7117100d53d8837
CLIENT_TRAFFIC_SECRET_0 7c89abb4c15b9439c95cd9acf9fb9d356ba8d0df3fe08e223b320c4284c29dab ed3361e0ac89f2ad963d9ede4a090f7f15214d5479106403e3e7cd2a98679cddec229c41a1558cd3da61242021d7ed2a
