# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 332e1bdf3024b54048030a8005099037b76fc90a9863f30936a77e8cd3317fff ac9486d06372f00e59982bf82726fb55cd930ef4872bbb819f9d429b71ab4e6339741cd0c25b25979415b4da26975c48
EXPORTER_SECRET 332e1bdf3024b54048030a8005099037b76fc90a9863f30936a77e8cd3317fff b432737022df830bc40e5995a4eb3050ff7df1ee949036b421ca87b705bda3db0ac8be7e37841ac8b77a1feb4179e724
SERVER_TRAFFIC_SECRET_0 332e1bdf3024b54048030a8005099037b76fc90a9863f30936a77e8cd3317fff 7510cb759948e0aec152823d82de14bc8fe33d2709e86398c917d5e7fc92537b7d949a30e06691ca9b4486b1129868ad
CLIENT_HANDSHAKE_TRAFFIC_SECRET 332e1bdf3024b54048030a8005099037b76fc90a9863f30936a77e8cd3317fff ff9bf1cafe0cd73649724c93e33df0d2ca743c470c88d1ad0807a5d2a776123ee1d3bfbd065d8802b9cfb0c44765766f
CLIENT_TRAFFIC_SECRET_0 332e1bdf3024b54048030a8005099037b76fc90a9863f30936a77e8cd3317fff f4e65a761966a1ad3220a06ffa03b474611e4b395b5fa00ec76c0cb0bc72c11c0c2804d76dd31efbbd50758aa9929b8c
