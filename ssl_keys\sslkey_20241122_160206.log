# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 24b338b716b15a5d7874afca93788bbde4b0058586e85b9d261f9880e93bcf0f a654d0a77df75779d611519d6e14e7fc30b5fb64d29862d14f77303e1cc7eda10906d8d10e2d1345ccd07d772e8d58d3
EXPORTER_SECRET 24b338b716b15a5d7874afca93788bbde4b0058586e85b9d261f9880e93bcf0f 577b08188a6057583c6a942e4adbb586222d3c6a55cdf212334d19a8a1b61070d59555511d22b789dbc336d101ec9122
SERVER_TRAFFIC_SECRET_0 24b338b716b15a5d7874afca93788bbde4b0058586e85b9d261f9880e93bcf0f ded81f42c8022627e22e63f0bc0fb634eaf624c03d5a30dfd10b239789e84c0bd5ae84b2ac5d896a6de22dd72c3e20ab
CLIENT_HANDSHAKE_TRAFFIC_SECRET 24b338b716b15a5d7874afca93788bbde4b0058586e85b9d261f9880e93bcf0f 181f75165d141ced67048655ec47103f1db0ad662b4969aaee80cd73d31aaf3b735f56842e6360948d77f120ef476a2b
CLIENT_TRAFFIC_SECRET_0 24b338b716b15a5d7874afca93788bbde4b0058586e85b9d261f9880e93bcf0f 58ecc13e559346ee05213df12f86f0cba1a9fd6a69babd6607d2b6d5ea16a3d8efa52179ae535ac9b52cf770501debe6
