# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 72d4a5cafa9663d575c98c918b5f37fbd68811b093ec3e752db074e40a1b5f95 05272bbd65b00f972788d26b678afad02fb0289b31dd44424f1135016f20a3a4c6b56b1d8720a8f24298aa1745116380
EXPORTER_SECRET 72d4a5cafa9663d575c98c918b5f37fbd68811b093ec3e752db074e40a1b5f95 05c900adc7be6e48656be95915f6607ec29ee0bb3e73cdb553f3ff0a154c1e02cb2e8362bdf40be568f211900bb4a97f
SERVER_TRAFFIC_SECRET_0 72d4a5cafa9663d575c98c918b5f37fbd68811b093ec3e752db074e40a1b5f95 b7871569fac326f48771b3d5053dfcf931f39c0b6a5d6a7d721ef34a1fa19c0a15f452adb0da08f5f89bdde3b2d80edb
CLIENT_HANDSHAKE_TRAFFIC_SECRET 72d4a5cafa9663d575c98c918b5f37fbd68811b093ec3e752db074e40a1b5f95 43894eacc18ec54f3ac2e49858c715f02f443638ed6805fd027ad5750092a8213c1e324a616b6fd385223595967cbaa8
CLIENT_TRAFFIC_SECRET_0 72d4a5cafa9663d575c98c918b5f37fbd68811b093ec3e752db074e40a1b5f95 6546ac04de231227b53cbc0f75213a016d73fd4ea7c4ba59ace418982ac71a89bb423d998f0dd6aec57df2d168d99f9c
