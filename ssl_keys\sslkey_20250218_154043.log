# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 7f7917b200657f83f76ae3b051ed0e2266883ecd29ebbbd2c37a5d08dbcd2481 aac7e918d99ffe4268a7491b13ec1f810765b9150617036ec455dce04afb95cebf1c442fdde48a3161cd7da31c0c91cd
EXPORTER_SECRET 7f7917b200657f83f76ae3b051ed0e2266883ecd29ebbbd2c37a5d08dbcd2481 329e863578d49824b5fd2fd8db361cbbfc65f05ce0211639b2cbf070b9a040b3a8d7c330f4c2e757832e191a5b2d4a8f
SERVER_TRAFFIC_SECRET_0 7f7917b200657f83f76ae3b051ed0e2266883ecd29ebbbd2c37a5d08dbcd2481 5a3ad8ee4aa6a65a45dc82db703ddcf1fe4a5189225bf22f6b5f1e03551fb97fae220404750159654a94fca333dac145
CLIENT_HANDSHAKE_TRAFFIC_SECRET 7f7917b200657f83f76ae3b051ed0e2266883ecd29ebbbd2c37a5d08dbcd2481 e57d575d33f82882ef78a5e61ae82b671efd0ed59affffbce3dd19fa3159f65610931517efcdc64e2bf7016c69e178ba
CLIENT_TRAFFIC_SECRET_0 7f7917b200657f83f76ae3b051ed0e2266883ecd29ebbbd2c37a5d08dbcd2481 8afa5d6423235e324e36a33fbb03ecc7048d0bc350ad822cbc468f002e5f6157351c0001a1c0c3b4ed965f13f4722d47
