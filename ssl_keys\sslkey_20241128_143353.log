# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET 60c3402290d9bfa1fc2928c679db2b7555ecfbcc2ab78163ba1ab133d369b6e8 f1df2bafb3df62d595be0d332692cbade355f1b1d01d955eda18697cc973810af70ca61da01d104d6243c9f02b68943a
EXPORTER_SECRET 60c3402290d9bfa1fc2928c679db2b7555ecfbcc2ab78163ba1ab133d369b6e8 b8489a4f93683d8919bba68261bc67ab05cd71d2a8e1b712bb8faf09e368ccd588ee344d4db2ca7f0b7717d1fc287d2d
SERVER_TRAFFIC_SECRET_0 60c3402290d9bfa1fc2928c679db2b7555ecfbcc2ab78163ba1ab133d369b6e8 52df081b9783627a66ff2d0e65dd61f7d3aa5611fe4cb8dbec8fab5e12e2f4e0bbc7b38edb2e06c793a0d06631363cb8
CLIENT_HANDSHAKE_TRAFFIC_SECRET 60c3402290d9bfa1fc2928c679db2b7555ecfbcc2ab78163ba1ab133d369b6e8 173a1e267244f877f1cae82374001d9de3c2833fec31ac06009bd487f00b4c7d013385963acecf7cbf6fbc8ac9b0fa91
CLIENT_TRAFFIC_SECRET_0 60c3402290d9bfa1fc2928c679db2b7555ecfbcc2ab78163ba1ab133d369b6e8 d5e1d567d3950711d753bbf3e743005cba27370d6598b6c3d8431d44216e2a60d6c00a7acb1cb943d42052db820976fc
