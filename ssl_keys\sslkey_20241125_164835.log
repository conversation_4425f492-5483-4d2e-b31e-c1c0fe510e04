# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET c7a790a542d7e66724e2d59a9ebbf4d137c4cf12f5bf5b19f831504129aefa89 a20c61d32e69a8a73abec6ec7bad0bfbd569f0f047acdd048ce0bab8e08619b12717f3ef187e5460c35fd581e1d9412b
EXPORTER_SECRET c7a790a542d7e66724e2d59a9ebbf4d137c4cf12f5bf5b19f831504129aefa89 805fa0dc460778eabb73b48c2c17818feb14d17f444602a28808b4468622b44da2941d84e9d916b2bf49e8f9fd00df09
SERVER_TRAFFIC_SECRET_0 c7a790a542d7e66724e2d59a9ebbf4d137c4cf12f5bf5b19f831504129aefa89 423e672a6673a43178bc04f350f2c0e5bc6f9fd57e7f4c84c5302f6e6d16f7bd3eb33c05aa658e1d8712f023877165f0
CLIENT_HANDSHAKE_TRAFFIC_SECRET c7a790a542d7e66724e2d59a9ebbf4d137c4cf12f5bf5b19f831504129aefa89 1895a503dd48b8a3779ca8647a826348a9fec9c287dc7748c0e5b65eeb0f9e13483e8dc3c89a0c75954dfa66d28a547d
CLIENT_TRAFFIC_SECRET_0 c7a790a542d7e66724e2d59a9ebbf4d137c4cf12f5bf5b19f831504129aefa89 2c42f986731d2923599dd2cc63ff8cd171f2f78caa2e0e15c04292b32f5517a2acd8fa5ae71d645f1a5e03a9619da892
