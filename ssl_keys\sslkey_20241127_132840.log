# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 559b74668c59b16eaf1dbee818ff6a62be83fb7a78a71a2bde28c194e3db1cb4 5e074c32a02009e2c7538631aed2187ebde7bbee662dea8eaf07eb5ceb296fd37dea22810b475893efca38fe64cff429
EXPORTER_SECRET 559b74668c59b16eaf1dbee818ff6a62be83fb7a78a71a2bde28c194e3db1cb4 f83309c69a03eefee3de92d88656767637eb7e8bc4761d25b39f0c0c2ed4e2a038d92e2fba3ccef2021be7cdeb9c3914
SERVER_TRAFFIC_SECRET_0 559b74668c59b16eaf1dbee818ff6a62be83fb7a78a71a2bde28c194e3db1cb4 bb7f4dcd84d7279f687ef49a72942866a5cb265dd08e34c6fa149679ebd08bf2042d2acd5198d3a535d10db6dcd74793
CLIENT_HANDSHAKE_TRAFFIC_SECRET 559b74668c59b16eaf1dbee818ff6a62be83fb7a78a71a2bde28c194e3db1cb4 f9323c1dd552acd4e9f1faea2788676a08472ed78f95db917711f52f271419c0bb7e86d7ba24b256be128236217634d4
CLIENT_TRAFFIC_SECRET_0 559b74668c59b16eaf1dbee818ff6a62be83fb7a78a71a2bde28c194e3db1cb4 f1b61db75025cfcb7536f2cf3fb6aad5b1e8aa6f6dfa6808677abe4ee77ce45dc3bed48a8a283ac92e4efece89f807b8
