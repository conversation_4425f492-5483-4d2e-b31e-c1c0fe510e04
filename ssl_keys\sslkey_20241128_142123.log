# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 0632ec067586fc52982bc28c821707ab48178b349273075a021aa5457b6eaabf 47312ec186900e0672c83102d41fe4e85fdf34f1301cc0240cf17cbdd065a564141e3545fd12bcd13796e4f3da3b48de
EXPORTER_SECRET 0632ec067586fc52982bc28c821707ab48178b349273075a021aa5457b6eaabf 72b48d25846217bef39f33e3c54d1f2831ca840d74fb350f25ebe0c7596318db7be94759e3f4fed60bc9b4a4b421f6d2
SERVER_TRAFFIC_SECRET_0 0632ec067586fc52982bc28c821707ab48178b349273075a021aa5457b6eaabf 402fcb74c5f314b199e2cb3b67520ecd19e15d0bbdc6e12b666d717a3dcf7d90d74b2dac972096af60e6748c65f77e50
CLIENT_HANDSHAKE_TRAFFIC_SECRET 0632ec067586fc52982bc28c821707ab48178b349273075a021aa5457b6eaabf 5ee31b8da9ef298e906311d40682071eab8f7347a5c7f796760e7ff40aeb35a98d130a1decae1bf79e2c6299fc437050
CLIENT_TRAFFIC_SECRET_0 0632ec067586fc52982bc28c821707ab48178b349273075a021aa5457b6eaabf 94e3b6e493f83dcc89c05d397671009b77c5d69209131ff5caa94cb92ec5e3eed5b1227300c072c2c360f8b704ce447a
