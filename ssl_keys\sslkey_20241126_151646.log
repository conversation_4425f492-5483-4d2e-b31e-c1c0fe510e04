# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET 9c1a6cb605ba4943e127e462ae539e8402de27e33f2432f8e39cac491a73d5a5 3a6ac53c335d57d95d9c77bd84966d3cfb05c3dcc5045ff269dab42a35775ef20e584134ebda8963ec85481d00958919
EXPORTER_SECRET 9c1a6cb605ba4943e127e462ae539e8402de27e33f2432f8e39cac491a73d5a5 ed87a863bd2da29913340f64c9a3d128ca3e6198f2a66df1f58ff153b058a33b1e2b1ed66723153f5d65eb943a3a5959
SERVER_TRAFFIC_SECRET_0 9c1a6cb605ba4943e127e462ae539e8402de27e33f2432f8e39cac491a73d5a5 7359a525d4005d76200392f2c727631712a809ec6d1195121674f153f562d86adfe679a7b77b22b2558f2c738b169c83
CLIENT_HANDSHAKE_TRAFFIC_SECRET 9c1a6cb605ba4943e127e462ae539e8402de27e33f2432f8e39cac491a73d5a5 bd0ded6142dd92a53595c96f738c317f13413f01d19ecc5ae726c0c2c8c814981d2eb5fee4a40b56e911a8c38a8cb4bc
CLIENT_TRAFFIC_SECRET_0 9c1a6cb605ba4943e127e462ae539e8402de27e33f2432f8e39cac491a73d5a5 74360e1ca6f0af52c51980fa0ec5eadd137368f7784f33576086cc5a297a65695b6f3799a3f1092b011b49eafc78b50b
