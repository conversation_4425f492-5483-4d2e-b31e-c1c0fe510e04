# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET b55784e0dfb6c56923b51f11f99ee4dd97845bd14b79e28bb0228188a9eeef08 b909cc323eb0cab3137e491a18c6b84aded500d51360ee2e497cac997a6c2efb94ee3b20195d0d4e4d6589e30e8efbf9
EXPORTER_SECRET b55784e0dfb6c56923b51f11f99ee4dd97845bd14b79e28bb0228188a9eeef08 f90bc9bb29307635bd902f52fff48acd7338237628baa482f9edf8da1ac36bea334cc88e9dccbbf0ccd85ae4a7198f29
SERVER_TRAFFIC_SECRET_0 b55784e0dfb6c56923b51f11f99ee4dd97845bd14b79e28bb0228188a9eeef08 00a48a2e493662e37782c832e098cde10e0c400d323a5026dee8bc434a90f9981167a5599afefc41d4fe214527500748
CLIENT_HANDSHAKE_TRAFFIC_SECRET b55784e0dfb6c56923b51f11f99ee4dd97845bd14b79e28bb0228188a9eeef08 d0a629980310ff94264699558ab04868c4eb87c52a790d3a22c6d39790859d4f7925bc0213d80253c504d88ccea06063
CLIENT_TRAFFIC_SECRET_0 b55784e0dfb6c56923b51f11f99ee4dd97845bd14b79e28bb0228188a9eeef08 e2e597e8647efcc4897eb4962bdff32e4fa062ba8f20549100a563f8194eb64fca09c7aa727d113bb89e018fd3e995ba
