# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON><PERSON><PERSON>_TRAFFIC_SECRET dae7fc1832309b465631685afb3027219dcc3fec16abf93d44d8089cac29cf08 41ebac0441474361835be2ae5da1a415a1b79a5e9daf0d93c7d2512fff9e17843e3c8730a16122908d470325145cc328
EXPORTER_SECRET dae7fc1832309b465631685afb3027219dcc3fec16abf93d44d8089cac29cf08 b4c94835c5c69c95544bd2efe6aadc1c67e98688ee35e75fd5c6a3160bbab4e6091f4bfbc9ebb95e955981cf41a6b686
SERVER_TRAFFIC_SECRET_0 dae7fc1832309b465631685afb3027219dcc3fec16abf93d44d8089cac29cf08 025348c971e14e68151a5acb9e58deaf2455c6ced3c8ad35853fc1e043479ebb822be6894b5a7aec1d6632eb4a0fe825
CLIENT_HANDSHAKE_TRAFFIC_SECRET dae7fc1832309b465631685afb3027219dcc3fec16abf93d44d8089cac29cf08 2e7211b3bf294a6d5e1438767032ae91bc03eb2cedc57f01c6a22520d7f0959e4ea15e00a489328ec012146e4369d109
CLIENT_TRAFFIC_SECRET_0 dae7fc1832309b465631685afb3027219dcc3fec16abf93d44d8089cac29cf08 eabcdc821a8a06e274cdf9013bbd65bd1976dd96e2624e4def65c8ff22444e162bd079f1d294ae37f938a003c693ba13
