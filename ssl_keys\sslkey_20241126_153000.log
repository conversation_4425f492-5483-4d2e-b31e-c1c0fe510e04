# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET bc3ca0270cf2d0d9d68cf4d876bb818f63270409ad8dbf15eee2d1203fd70dd2 b90a76eca695713ea2ea8a24bacf245759aeb27ad894db4c1af2d38a72b9494583e9a1db8facc4c25568cc42acb76a17
EXPORTER_SECRET bc3ca0270cf2d0d9d68cf4d876bb818f63270409ad8dbf15eee2d1203fd70dd2 42c65582e50e51aef5c2d4744a599a0fba9302f1076543d70c62efa4adedc487f4d42243a702a574a4bdd7bdf7776bb4
SERVER_TRAFFIC_SECRET_0 bc3ca0270cf2d0d9d68cf4d876bb818f63270409ad8dbf15eee2d1203fd70dd2 267de4c5c8562bc96cedbf2ff82f983238315b282f8a6dfd09df6c7060e8c240e58f74adfaba064bd6afbe8f5d0ede06
CLIENT_HANDSHAKE_TRAFFIC_SECRET bc3ca0270cf2d0d9d68cf4d876bb818f63270409ad8dbf15eee2d1203fd70dd2 d78e34215d2d7bec3b8f9ebfe14f6b8f02ee2aebf63f0ecfd6dd204569485d2a8f40d63ff1254736d4a1e2e3328d58d9
CLIENT_TRAFFIC_SECRET_0 bc3ca0270cf2d0d9d68cf4d876bb818f63270409ad8dbf15eee2d1203fd70dd2 ce4c518e60f493b2775a184ea44f83a80d76ae6aafdc7ab5e57b16ce7b5be0f147d3b623bf3d18942252cd438889ab7d
