# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET f1f8413c3f13e8d2d789ea2229bbbf8e32c76f3f24ed81a15d6c39deda456440 9fe2953fe6b65a1c26e209acc093d1b2e1ad5de0764c52bd0be52bc6936a7b4df56822926e765d5c2b73c6de9dd17ccf
EXPORTER_SECRET f1f8413c3f13e8d2d789ea2229bbbf8e32c76f3f24ed81a15d6c39deda456440 f984c5cb89debc63f7c48b61b0e9a348dfd10e57909f3c1338b5ec70d157e425508bee71b3457b39b3bf3cc83be9d676
SERVER_TRAFFIC_SECRET_0 f1f8413c3f13e8d2d789ea2229bbbf8e32c76f3f24ed81a15d6c39deda456440 843ddf5b64832db4127f947734bb5b73be2051076672c0ffad5e4b84d97ad2acdbf705f69f4fad068e46f29322e53ca7
CLIENT_HANDSHAKE_TRAFFIC_SECRET f1f8413c3f13e8d2d789ea2229bbbf8e32c76f3f24ed81a15d6c39deda456440 68f8d3daa6cff9af1b651c09425179d1cfa526a23e33f1cde1e0b6e713d2b93bbed7b8bcc08f984f414c73098eb8e0f4
CLIENT_TRAFFIC_SECRET_0 f1f8413c3f13e8d2d789ea2229bbbf8e32c76f3f24ed81a15d6c39deda456440 6bc63c672eb5811b3338bf92020adc0ca8294c3b15307aad3e04d37ccb6c0e04e92ba487073e32f9e36c9380a875a6ce
