# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 71e1ca0266bd424f42df92eb8b1daba0c8a792914b219fa02ae8b57c8919a203 c4ff4a808b191893b77ca3be8f095a487f64b28c93a0d79757b0eb9d21edde9daa6b04997c5bc89d70ef46d9f4eedba3
EXPORTER_SECRET 71e1ca0266bd424f42df92eb8b1daba0c8a792914b219fa02ae8b57c8919a203 7a72c975e114d2a510c22b46fdbe44523574fed567a8e5b21afd9bd0daa1812d4ebda03b86559dc247aaac4de2ccd4d5
SERVER_TRAFFIC_SECRET_0 71e1ca0266bd424f42df92eb8b1daba0c8a792914b219fa02ae8b57c8919a203 38730f3a0c4f1f908dfc5cfd518b6feb6a2022f66e6674e1da485b74db2604902ab10b8ffe47175b0679836c45662b8f
CLIENT_HANDSHAKE_TRAFFIC_SECRET 71e1ca0266bd424f42df92eb8b1daba0c8a792914b219fa02ae8b57c8919a203 6507af40496f3a17443f90d681dc944cc2e08aca672ddb071f0ef76b781c933552e3c29e366fbfd7223084701f1e7594
CLIENT_TRAFFIC_SECRET_0 71e1ca0266bd424f42df92eb8b1daba0c8a792914b219fa02ae8b57c8919a203 a858daf79054caab08ad8dff229cfa1ad178d39c2f1adeba4870b0e80d7847a0e3ca1aebb43272a739f6c8a16fcf4a5a
