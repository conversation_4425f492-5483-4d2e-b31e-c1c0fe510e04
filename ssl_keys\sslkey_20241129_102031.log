# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON><PERSON>KE_TRAFFIC_SECRET 16f11e86b08de8c371bc2b53d1c1dcb85d64c99e50f6e0966e90e3caad0609c0 45d10588f4cae12f27445d374662d4860c1ba6837f38cc37cdb8a15cf636184b705001ec698b749d158279424dd6e832
EXPORTER_SECRET 16f11e86b08de8c371bc2b53d1c1dcb85d64c99e50f6e0966e90e3caad0609c0 185e5a268b9b10eba92410506561775185ebc7e5b26cc414c5fed9e06bcb09a63726f806b459c6c5315660f9fa796455
SERVER_TRAFFIC_SECRET_0 16f11e86b08de8c371bc2b53d1c1dcb85d64c99e50f6e0966e90e3caad0609c0 6566469397b2bcf503ec4399cf193ec40207e9391b5d1971680803ecde4e204373354845aa13c503efe3cd6e2c7e7acd
CLIENT_HANDSHAKE_TRAFFIC_SECRET 16f11e86b08de8c371bc2b53d1c1dcb85d64c99e50f6e0966e90e3caad0609c0 6c99a7a385545acd40a96ac7a490eb49c2b33ae6a1b98f86bb8afca7a9dd7b8062221908f78d62fa1fe4c7a9b15c3ec0
CLIENT_TRAFFIC_SECRET_0 16f11e86b08de8c371bc2b53d1c1dcb85d64c99e50f6e0966e90e3caad0609c0 be37ab1f39407c406f45a984aa7568b727e225428b41d5d47448771211df2fcc071f3954dab5a01f889949a6594a4468
