# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET df857a8a933a9891f01521c0ad3640c99bdc2cef995ef55f8346b4c934654382 4a3607860deac732682e7972c3cc1d64c61ef2350d95c6ecab0358ce861d1a6f15ad481f3d6fcf2cf4cf62f04c8c5071
EXPORTER_SECRET df857a8a933a9891f01521c0ad3640c99bdc2cef995ef55f8346b4c934654382 4605fe6fcf5a874b5b626b2aa3e121c7f167b1bb1bf30bef4609d93e09c9c228c5dcb19975d22de9587091306a584cb8
SERVER_TRAFFIC_SECRET_0 df857a8a933a9891f01521c0ad3640c99bdc2cef995ef55f8346b4c934654382 77af52e1a3ede94f3a0d7368105c9565eadf56152729076707cb34ac7068459533dd40db6c3c58cafecc7e613a199f04
CLIENT_HANDSHAKE_TRAFFIC_SECRET df857a8a933a9891f01521c0ad3640c99bdc2cef995ef55f8346b4c934654382 930a89a5e42ccb0f4f1f414d87d563285e26a009ffa5a6cddadb414d26e8e1de55c1f436a61d1ba1f4cb98903ca54678
CLIENT_TRAFFIC_SECRET_0 df857a8a933a9891f01521c0ad3640c99bdc2cef995ef55f8346b4c934654382 b0b891eab0039751d08a6fd60be97d11ce1cdae02bce0b0ebc2ae77b731696f4de322b14b0a36e35ec97c6a011ef80ef
