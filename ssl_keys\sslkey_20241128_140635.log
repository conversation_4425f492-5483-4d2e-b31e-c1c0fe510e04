# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HANDSHAKE_TRAFFIC_SECRET 8a2f7ce2fdde5c7e230ec3db966af7218d27ae0f99f66ff859c7e95d3252c3a8 45f2b02c369aa73446b326e5e5a67da7217fbe7d0ea9b63cd9a6a1b8d7a16943630d642588f90f6a609bf443fb91dab8
EXPORTER_SECRET 8a2f7ce2fdde5c7e230ec3db966af7218d27ae0f99f66ff859c7e95d3252c3a8 59f75023a9b56a201e4ee84207b581e23a1cb65f7dd7b28a3bc63946399397a208e2222c005bc3055190dc6b8c3f3844
SERVER_TRAFFIC_SECRET_0 8a2f7ce2fdde5c7e230ec3db966af7218d27ae0f99f66ff859c7e95d3252c3a8 1400367cf10d7f604b9548317dcbf38c22c67f11a2eea24c8b19fddff4bea5652025b27994dc4427fcf00e2972ae48af
CLIENT_HANDSHAKE_TRAFFIC_SECRET 8a2f7ce2fdde5c7e230ec3db966af7218d27ae0f99f66ff859c7e95d3252c3a8 270a05b9f9f1a21ba45e9962b6a3d7a7c6eb87fbc7b9267347f140b54b93724a31e7209cee44a8d385d883f12ea497bb
CLIENT_TRAFFIC_SECRET_0 8a2f7ce2fdde5c7e230ec3db966af7218d27ae0f99f66ff859c7e95d3252c3a8 6d8b88385d262e912fa269138e9e35e9cd69ebd0045da318bd3c7ac2ce64f1434bbfe59ac4b14ae6229ece2a3204e991
