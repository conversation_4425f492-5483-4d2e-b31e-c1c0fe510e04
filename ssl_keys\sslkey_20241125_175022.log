# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET 8e370708aeb6739623b1ec5b5dfec289b70ffbe9d35fbb3c038424ba8531851d d19229245f43ad2f97a505bbb41b1a70403c1cece91a9c5198c1a7578a12acbe067c9e02754791c20e28ce5b20077522
EXPORTER_SECRET 8e370708aeb6739623b1ec5b5dfec289b70ffbe9d35fbb3c038424ba8531851d e6f48ea657a8d20344aeea4938cb03ebbc925ee78ae95999cd088d823bbd53fad032ed396aed303a84c998df138fd35d
SERVER_TRAFFIC_SECRET_0 8e370708aeb6739623b1ec5b5dfec289b70ffbe9d35fbb3c038424ba8531851d e6a6eec234166569b24b5d9b7cf2486ca87a069a993312461e3880d6cf8f51844b9ad90def6ac2e46f2d6a6a65ea40dd
CLIENT_HANDSHAKE_TRAFFIC_SECRET 8e370708aeb6739623b1ec5b5dfec289b70ffbe9d35fbb3c038424ba8531851d ab3a5afde62321b0df76f11611cb67a1599c5beb92adb079950c7ab7a91e7f06a80fefb1a2ae9eb1e148d739a1214440
CLIENT_TRAFFIC_SECRET_0 8e370708aeb6739623b1ec5b5dfec289b70ffbe9d35fbb3c038424ba8531851d 06f14a8c45e754db20d3bc8cb4e2b4fcd6b21af0b334be0f60f5ea804d01aaaec13de06e4c390d824454bb661285b47b
