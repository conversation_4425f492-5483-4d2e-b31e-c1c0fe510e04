#!/usr/bin/env python3
"""
Test script for DoIP keep alive functionality
"""

import sys
import socket
import time

def test_alive_check():
    """Test sending DoIP alive check request"""
    # DoIP alive check request: 02 fd 00 07 00 00 00 00
    alive_request = bytes([0x02, 0xfd, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00])
    
    try:
        # Connect to the DoIP tool (assuming it's running on localhost:13400)
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(('127.0.0.1', 13400))
        
        print("Connected to DoIP tool")
        
        # Send alive check request
        print("Sending alive check request...")
        sock.send(alive_request)
        
        # Wait for response
        sock.settimeout(5.0)
        response = sock.recv(1024)
        
        if len(response) >= 10:
            print(f"Received response: {' '.join(f'{b:02X}' for b in response)}")
            
            # Check if it's the expected alive check response
            if (response[0] == 0x02 and response[1] == 0xfd and 
                response[2] == 0x00 and response[3] == 0x08):
                print("✓ Alive check response received correctly!")
            else:
                print("✗ Unexpected response format")
        else:
            print("✗ No response or incomplete response")
            
        sock.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_alive_check()
