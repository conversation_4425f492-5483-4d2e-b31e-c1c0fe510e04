# SSL/TLS secrets log file, generated by DoIP Tool
# TLS secrets log file, generated by OpenSSL / Python
SERVER_HAN<PERSON>HAKE_TRAFFIC_SECRET 86c3b840f2654a3cdc656ae66622e9943a0f263a90dad8924e2791cccfcacac8 1e885cf34dd7bae2c09e00b867e20a6fc6ea2609d06516100b0c88c301a40d8d653536680c0873e5b0d3ac745409920e
EXPORTER_SECRET 86c3b840f2654a3cdc656ae66622e9943a0f263a90dad8924e2791cccfcacac8 c96fad3bf3381754e02e568710d2743144d9973957a3b25b1857a1fbafa6a87a47e8d17e18543c57702f7d0fb3d3bf75
SERVER_TRAFFIC_SECRET_0 86c3b840f2654a3cdc656ae66622e9943a0f263a90dad8924e2791cccfcacac8 d55b7c16e08cc1eb8b440f409012385d2a2bdf42d55c5d6a625c19535a8ae9c02d9b790d8209db5622a5afac04ad899f
CLIENT_HANDSHAKE_TRAFFIC_SECRET 86c3b840f2654a3cdc656ae66622e9943a0f263a90dad8924e2791cccfcacac8 322daf67855b1a8676aeb02b19e11f6bb22133645233abb74e3476c2954ff1ccb4ebd3874b81e3b54fa0e91d494ea22a
CLIENT_TRAFFIC_SECRET_0 86c3b840f2654a3cdc656ae66622e9943a0f263a90dad8924e2791cccfcacac8 2fad0874d5a89ca3af2489a57fd6cf3192569e9a066f85864ca0c9a4cfe11909ec4c17a19b4c66edc44551ee98a859f6
